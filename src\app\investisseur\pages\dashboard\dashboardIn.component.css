/* Variables pour le dashboard investisseur */
:root {
  --dashboard-primary: #1e3a8a;
  --dashboard-primary-light: #3b82f6;
  --dashboard-success: #10b981;
  --dashboard-warning: #f59e0b;
  --dashboard-danger: #ef4444;
  --dashboard-purple: #8b5cf6;
  --dashboard-bg: #f8fafc;
  --dashboard-card-bg: #ffffff;
  --dashboard-text-primary: #1e293b;
  --dashboard-text-secondary: #64748b;
  --dashboard-text-light: #94a3b8;
  --dashboard-border: #e2e8f0;
  --dashboard-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  --dashboard-shadow-hover: 0 8px 32px rgba(0, 0, 0, 0.12);
  --dashboard-radius: 16px;
  --dashboard-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* En-tête du Dashboard */
.dashboard-header {
  background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
  border-radius: 16px;
  padding: 2rem;
  margin-bottom: 2rem;
  color: white;
  position: relative;
  overflow: hidden;
}

.dashboard-header::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 200px;
  height: 200px;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
  border-radius: 50%;
  transform: translate(50%, -50%);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  z-index: 1;
}

.welcome-section {
  flex: 1;
}

.dashboard-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.title-icon {
  font-size: 2rem;
  opacity: 0.9;
}

.dashboard-subtitle {
  font-size: 1.1rem;
  opacity: 0.9;
  margin: 0;
  font-weight: 400;
}

.market-status-widget {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  align-items: flex-end;
}

.market-indicator {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  background: rgba(255, 255, 255, 0.15);
  padding: 0.75rem 1.25rem;
  border-radius: 25px;
  backdrop-filter: blur(10px);
}

.market-dot {
  width: 12px;
  height: 12px;
  background: #10b981;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.market-info {
  display: flex;
  flex-direction: column;
}

.market-label {
  font-size: 0.8rem;
  opacity: 0.8;
}

.market-status {
  font-size: 0.9rem;
  font-weight: 600;
}

.market-time {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.time-label {
  font-size: 0.75rem;
  opacity: 0.7;
}

.time-value {
  font-size: 0.85rem;
  font-weight: 500;
}

/* Section KPI Ultra-Moderne */
.kpi-dashboard {
  margin-bottom: 3rem;
}

.kpi-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.kpi-card {
  background: #ffffff;
  border-radius: 16px;
  padding: 1.5rem;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid#e2e8f0;
  position: relative;
  overflow: hidden;
}

.kpi-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #1e3a8a, #3b82f6);
  opacity: 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.kpi-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
}

.kpi-card:hover::before {
  opacity: 1;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.icon-container {
  width: 56px;
  height: 56px;
  border-radius: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
  position: relative;
}

.balance-icon {
  background: linear-gradient(135deg, #10b981, #34d399);
}

.portfolio-icon {
  background: linear-gradient(135deg, #1e3a8a, #3b82f6);
}

.assets-icon {
  background: linear-gradient(135deg, #8b5cf6, #a855f7);
}

.orders-icon {
  background: linear-gradient(135deg, #f59e0b, #fbbf24);
}

.performance-icon {
  background: linear-gradient(135deg, #ef4444, #f87171);
}

/* Indicateurs de tendance */
.trend-indicator {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
}

.trend-indicator.positive {
  background: rgba(16, 185, 129, 0.1);
  color: #10b981;
}

.trend-indicator.negative {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

/* Badges et statuts */
.asset-count-badge {
  background: linear-gradient(135deg, #8b5cf6, #a855f7);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
}

.orders-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #10b981;
  animation: pulse 2s infinite;
}

.status-text {
  font-size: 0.8rem;
  color: #64748b;
  font-weight: 500;
}

.performance-badge {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
}

/* Contenu des cartes */
.card-content {
  margin-bottom: 1rem;
}

.kpi-label {
  font-size: 0.9rem;
  color: #64748b;
  margin: 0 0 0.5rem 0;
  font-weight: 500;
}

.kpi-value-container {
  display: flex;
  align-items: baseline;
  gap: 0.5rem;
  margin-bottom: 0.25rem;
}

.kpi-value {
  font-size: 2rem;
  font-weight: 700;
  color: #1e293b;
  line-height: 1;
}

.currency {
  font-size: 1rem;
  color: #64748b;
  font-weight: 500;
}

.kpi-subtitle {
  font-size: 0.8rem;
  color: #94a3b8;
}

/* Pieds de cartes */
.card-footer {
  margin-top: auto;
}

.progress-bar {
  width: 100%;
  height: 4px;
  background: #e2e8f0;
  border-radius: 2px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #10b981, #34d399);
  border-radius: 2px;
  transition: width 0.5s ease;
}

.mini-chart {
  height: 40px;
  position: relative;
  overflow: hidden;
}

.chart-line {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, #1e3a8a, #3b82f6);
  border-radius: 1px;
  animation: chartGrow 2s ease-out;
}

.asset-types {
  display: flex;
  gap: 0.5rem;
}

.asset-type {
  padding: 0.25rem 0.5rem;
  background: #e2e8f0;
  border-radius: 8px;
  font-size: 0.7rem;
  color: #64748b;
}

.order-stats {
  display: flex;
  justify-content: space-between;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.25rem;
}

.stat-label {
  font-size: 0.7rem;
  color: #94a3b8;
}

.stat-value {
  font-size: 0.8rem;
  font-weight: 600;
  color: #1e293b;
}

/* Anneau de performance */
.performance-ring {
  width: 60px;
  height: 60px;
  margin: 0 auto;
}

.ring-svg {
  width: 100%;
  height: 100%;
  transform: rotate(-90deg);
}

.ring-bg {
  fill: none;
  stroke: #e2e8f0;
  stroke-width: 2;
}

.ring-progress {
  fill: none;
  stroke: #ef4444;
  stroke-width: 2;
  stroke-linecap: round;
  transition: stroke-dasharray 0.5s ease;
}

/* Section Analytics */
.analytics-section {
  margin-bottom: 3rem;
}

.section-header {
  margin-bottom: 2rem;
  text-align: center;
}

.section-title {
  font-size: 2rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 0.5rem 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
}

.section-icon {
  font-size: 1.8rem;
  color: #1e3a8a;
}

.section-subtitle {
  font-size: 1rem;
  color: #64748b;
  margin: 0;
}

.charts-grid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 1.5rem;
}

.chart-container {
  background: #ffffff;
  border-radius: 16px;
  padding: 1.5rem;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  border: 1px solid #e2e8f0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.chart-container:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
}

.chart-container.full-width {
  grid-column: 1 / -1;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e2e8f0;
}

.chart-title-section {
  flex: 1;
}

.chart-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 0.25rem 0;
}

.chart-description {
  font-size: 0.9rem;
  color: #64748b;
  margin: 0;
}

.chart-actions {
  display: flex;
  gap: 0.5rem;
}

.chart-action-btn {
  width: 36px;
  height: 36px;
  border: none;
  background: #e2e8f0;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  color: #64748b;
}

.chart-action-btn:hover {
  background: #1e3a8a;
  color: white;
  transform: scale(1.05);
}

.chart-filters {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.filter-select {
  padding: 0.5rem 1rem;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  background: white;
  color: #1e293b;
  font-size: 0.9rem;
  cursor: pointer;
}

.chart-content {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.chart-wrapper {
  flex: 1;
  min-height: 300px;
}

.chart-wrapper.full-height {
  min-height: 400px;
}

.chart-insights {
  display: flex;
  justify-content: space-around;
  padding: 1rem;
  background: #f8fafc;
  border-radius: 12px;
}

.insight-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.25rem;
}

.insight-label {
  font-size: 0.8rem;
  color: #94a3b8;
}

.insight-value {
  font-size: 0.9rem;
  font-weight: 600;
  color: #1e293b;
}

.chart-legend-custom {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  padding: 1rem;
  background: #f8fafc;
  border-radius: 12px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 3px;
}

.legend-label {
  flex: 1;
  font-size: 0.9rem;
  color: #1e293b;
}

.legend-value {
  font-size: 0.9rem;
  font-weight: 600;
  color: #64748b;
}

.chart-stats {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

.stat-card {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem;
  background: #f8fafc;
  border-radius: 12px;
  flex: 1;
}

.stat-icon {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  color: white;
}

.stat-icon.success {
  background: linear-gradient(135deg, #10b981, #34d399);
}

.stat-icon.pending {
  background: linear-gradient(135deg, #f59e0b, #fbbf24);
}

.stat-icon.cancelled {
  background: linear-gradient(135deg, #ef4444, #f87171);
}

.stat-info {
  display: flex;
  flex-direction: column;
}

.stat-info .stat-label {
  font-size: 0.8rem;
  color: #94a3b8;
}

.stat-info .stat-value {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1e293b;
}

/* Section Actions Rapides */
.quick-actions-section {
  margin-bottom: 2rem;
}

.quick-actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.quick-action-btn {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  background: #ffffff;
  border: 1px solid #e2e8f0;
  border-radius: 16px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  text-align: left;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}

.quick-action-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
}

.quick-action-btn.primary {
  border-left: 4px solid #1e3a8a;
}

.quick-action-btn.primary:hover {
  background: linear-gradient(135deg, rgba(30, 58, 138, 0.05), rgba(59, 130, 246, 0.05));
}

.quick-action-btn.secondary {
  border-left: 4px solid #10b981;
}

.quick-action-btn.secondary:hover {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.05), rgba(52, 211, 153, 0.05));
}

.quick-action-btn.tertiary {
  border-left: 4px solid #f59e0b;
}

.quick-action-btn.tertiary:hover {
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.05), rgba(251, 191, 36, 0.05));
}

.quick-action-btn.quaternary {
  border-left: 4px solid #8b5cf6;
}

.quick-action-btn.quaternary:hover {
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.05), rgba(168, 85, 247, 0.05));
}

.action-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
}

.quick-action-btn.primary .action-icon {
  background: linear-gradient(135deg, #1e3a8a, #3b82f6);
}

.quick-action-btn.secondary .action-icon {
  background: linear-gradient(135deg, #10b981, #34d399);
}

.quick-action-btn.tertiary .action-icon {
  background: linear-gradient(135deg, #f59e0b, #fbbf24);
}

.quick-action-btn.quaternary .action-icon {
  background: linear-gradient(135deg, #8b5cf6, #a855f7);
}

.action-content {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.action-title {
  font-size: 1rem;
  font-weight: 600;
  color: #1e293b;
}

.action-subtitle {
  font-size: 0.85rem;
  color: #64748b;
}

/* Animations */
@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }

  50% {
    transform: scale(1.05);
    opacity: 0.7;
  }

  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes chartGrow {
  from {
    transform: scaleX(0);
  }

  to {
    transform: scaleX(1);
  }
}

/* Styles responsifs */
@media (max-width: 1200px) {
  .dashboard-title {
    font-size: 2rem;
  }

  .kpi-grid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  }

  .charts-grid {
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
  }
}

@media (max-width: 992px) {
  .charts-grid {
    grid-template-columns: 1fr 1fr;
    gap: 1.25rem;
  }
}

@media (max-width: 768px) {
  .dashboard-header {
    padding: 1.5rem;
  }

  .header-content {
    flex-direction: column;
    gap: 1.5rem;
    text-align: center;
  }

  .dashboard-title {
    font-size: 1.8rem;
  }

  .market-status-widget {
    align-items: center;
  }

  .kpi-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .kpi-card {
    padding: 1.25rem;
  }

  .kpi-value {
    font-size: 1.75rem;
  }

  .charts-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .chart-container {
    padding: 1.25rem;
  }

  .chart-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .chart-filters {
    justify-content: space-between;
  }

  .chart-stats {
    flex-direction: column;
    gap: 0.75rem;
  }

  .quick-actions-grid {
    grid-template-columns: 1fr;
  }

  .section-title {
    font-size: 1.5rem;
    flex-direction: column;
    gap: 0.5rem;
  }
}

@media (max-width: 480px) {
  .dashboard-header {
    padding: 1rem;
  }

  .dashboard-title {
    font-size: 1.5rem;
  }

  .kpi-card {
    padding: 1rem;
  }

  .chart-container {
    padding: 1rem;
  }

  .quick-action-btn {
    padding: 1.25rem;
  }

  .action-icon {
    width: 40px;
    height: 40px;
    font-size: 1.25rem;
  }
}