<!-- Container principal VERMEG ultra-moderne -->
<div class="register-container">
    <!-- Background animé avec particules -->
    <div class="animated-background">
        <!-- Particules flottantes avec couleurs VERMEG -->
        <div class="particle particle-1"></div>
        <div class="particle particle-2"></div>
        <div class="particle particle-3"></div>
        <div class="particle particle-4"></div>
        <div class="particle particle-5"></div>
        <div class="particle particle-6"></div>
        <div class="particle particle-7"></div>
        <div class="particle particle-8"></div>
        <div class="particle particle-9"></div>
        <div class="particle particle-10"></div>

        <!-- Orbes de lumière -->
        <div class="light-orb orb-1"></div>
        <div class="light-orb orb-2"></div>
        <div class="light-orb orb-3"></div>

        <!-- Grille de fond -->
        <div class="grid-overlay"></div>
    </div>

   <!-- Panneau branding VERMEG -->
    <div class="branding-panel">
        <div class="branding-content">
            <!-- Logo VERMEG -->
            <div class="vermeg-logo">
                <div class="logo-container">
                    <div class="logo-icon">
                        <i class="pi pi-chart-line"></i>
                    </div>
                    <div class="logo-pulse"></div>
                </div>
                <h1 class="company-name">VERMEG</h1>
                <p class="company-tagline">Financial Technology Solutions</p>
            </div>

            <!-- Statistiques animées -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon investisseur">
                        <i class="pi pi-chart-line"></i>
                    </div>
                    <div class="stat-info">
                        <span class="stat-number">2.4M+</span>
                        <span class="stat-label">Transactions</span>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon intermediaire">
                        <i class="pi pi-users"></i>
                    </div>
                    <div class="stat-info">
                        <span class="stat-number">150K+</span>
                        <span class="stat-label">Utilisateurs</span>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="quote-icon">
                        <i class="pi pi-globe"></i>
                    </div>
                    <div class="stat-info">
                        <span class="stat-number">25+</span>
                        <span class="stat-label">Pays</span>
                    </div>
                </div>
            </div>

            <!-- Citation inspirante -->
           
        </div>
    </div>

    <!-- Contenu principal formulaire -->
    <div class="form-container">
        <!-- Navigation tabs -->
       
        <!-- Carte formulaire principale -->
        <div class="register-card">
            <!-- Header -->
            <div class="card-header">
                <div class="header-icon">
                    <div class="icon-glow"></div>
                </div>
                <h2 class="card-title">Créer un compte</h2>
                <p class="card-subtitle">Rejoignez la plateforme VERMEG</p>
            </div>

            <!-- Formulaire d'inscription -->
            <form [formGroup]="registerForm" (ngSubmit)="onSubmit()" class="register-form">

                <div class="form-content">
                    <!-- Section Informations personnelles -->
                    <div class="form-section">
                        <h3 class="section-title">
                            <i class="pi pi-user"></i>
                            Informations personnelles
                        </h3>

                        <div class="form-grid">
                            <!-- Nom d'utilisateur -->
                            <div class="field-group">
                                <label class="field-label">Nom d'utilisateur</label>
                                <div class="input-wrapper">
                                    <div class="input-icon">
                                        <i class="pi pi-user"></i>
                                    </div>
                                    <input formControlName="username" type="text" class="form-input"
                                        placeholder="Votre nom d'utilisateur" [class.error]="isFieldInvalid('username')"
                                        autocomplete="username">
                                    <div class="input-border"></div>
                                </div>
                                <div class="field-error" *ngIf="isFieldInvalid('username')">
                                    <i class="pi pi-exclamation-circle"></i>
                                    {{ getFieldError('username') }}
                                </div>
                            </div>

                            <!-- Email -->
                            <div class="field-group">
                                <label class="field-label">Adresse email</label>
                                <div class="input-wrapper">
                                    <div class="input-icon">
                                        <i class="pi pi-envelope"></i>
                                    </div>
                                    <input formControlName="email" type="email" class="form-input"
                                        placeholder="<EMAIL>" [class.error]="isFieldInvalid('email')"
                                        autocomplete="email">
                                    <div class="input-border"></div>
                                </div>
                                <div class="field-error" *ngIf="isFieldInvalid('email')">
                                    <i class="pi pi-exclamation-circle"></i>
                                    {{ getFieldError('email') }}
                                </div>
                            </div>
                            
                        </div>
                    </div>

                    <!-- Section Sécurité -->
                    <div class="form-section">
                        <h3 class="section-title">
                            <i class="pi pi-shield"></i>
                            Sécurité du compte
                        </h3>

                        <div class="form-grid">
                            <!-- Mot de passe -->
                            <div class="field-group">
                                <label class="field-label">Mot de passe</label>
                                <div class="input-wrapper">
                                    <div class="input-icon">
                                        <i class="pi pi-lock"></i>
                                    </div>
                                    <input formControlName="password" [type]="showPassword ? 'text' : 'password'"
                                        class="form-input" placeholder="Votre mot de passe"
                                        [class.error]="isFieldInvalid('password')" autocomplete="new-password">
                                    <button type="button" class="password-toggle" (click)="togglePasswordVisibility()">
                                        <i class="pi" [class]="showPassword ? 'pi-eye-slash' : 'pi-eye'"></i>
                                    </button>
                                    <div class="input-border"></div>
                                </div>

                                <!-- Indicateur de force -->
                                <div class="password-strength" *ngIf="registerForm.get('password')?.value">
                                    <div class="strength-bar">
                                        <div class="strength-fill" [class]="getPasswordStrength()"
                                            [style.width.%]="getPasswordStrengthPercentage()"></div>
                                    </div>
                                    <div class="strength-text" [class]="getPasswordStrength()">
                                        {{ getPasswordStrengthText() }}
                                    </div>
                                </div>

                                <div class="field-error" *ngIf="isFieldInvalid('password')">
                                    <i class="pi pi-exclamation-circle"></i>
                                    {{ getFieldError('password') }}
                                </div>
                            </div>

                            <!-- Confirmation mot de passe -->
                            <div class="field-group">
                                <label class="field-label">Confirmer le mot de passe</label>
                                <div class="input-wrapper">
                                    <div class="input-icon">
                                        <i class="pi pi-lock"></i>
                                    </div>
                                    <input formControlName="confirmPassword"
                                        [type]="showConfirmPassword ? 'text' : 'password'" class="form-input"
                                        placeholder="Confirmez votre mot de passe"
                                        [class.error]="isFieldInvalid('confirmPassword')" autocomplete="new-password">
                                    <button type="button" class="password-toggle"
                                        (click)="toggleConfirmPasswordVisibility()">
                                        <i class="pi" [class]="showConfirmPassword ? 'pi-eye-slash' : 'pi-eye'"></i>
                                    </button>
                                    <div class="input-border"></div>
                                </div>
                                <div class="field-error" *ngIf="isFieldInvalid('confirmPassword')">
                                    <i class="pi pi-exclamation-circle"></i>
                                    {{ getFieldError('confirmPassword') }}
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- Bouton de soumission -->
                    <div class="form-actions">
                        <button type="submit" class="submit-btn" [disabled]="registerForm.invalid || isSubmitting">
                            <div class="btn-content" *ngIf="!isSubmitting">
                                <i class="pi pi-user-plus"></i>
                                <span>Créer mon compte</span>
                            </div>
                            <div class="btn-loading" *ngIf="isSubmitting">
                                <div class="spinner"></div>
                                <span>Création en cours...</span>
                            </div>
                        </button>
                    </div>
                </div>
            </form>

            <!-- Footer avec lien de connexion -->
            <div class="card-footer">
                <p class="footer-text">
                    Déjà un compte ?
                    <a (click)="router.navigate(['/login'])" class="footer-link">
                        Se connecter
                        <i class="pi pi-arrow-right"></i>
                    </a>
                </p>
            </div>
        </div>
    </div>
</div>