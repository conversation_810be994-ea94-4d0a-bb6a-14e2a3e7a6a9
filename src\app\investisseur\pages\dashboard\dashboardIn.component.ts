import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { PortefeuilleService } from '../../services/portefeuille.service';
import { KpiPortefeuilleDto } from '../../models/portefeuille';
import { CardModule } from 'primeng/card';
import { PanelModule } from 'primeng/panel';
import { ChartModule } from 'primeng/chart';
import { OrdreService } from '../../services/ordre.service';
import { KpiOrdreResponse } from '../../models/ordre';
@Component({
  selector: 'app-dashboardIn',
  standalone: true,
  imports: [CommonModule, CardModule, PanelModule, ChartModule],
  templateUrl: './dashboardIn.component.html',
  styleUrls: ['./dashboardIn.component.css'] // ✅ correct (tableau attendu)
})
export class DashboardInComponent implements OnInit {
  kpi?: KpiPortefeuilleDto;
  kpiData!: KpiOrdreResponse; // Interface pour les KPI d’ordres
  typeLabels: string[] = [];
  typeValues: number[] = [];

  statutLabels: string[] = [];
  statutValues: number[] = [];
  pieChartData: any;
  barChartData: any;
  typeChartData: any;
  statutChartData: any;


  pieChartOptions: any;
  barChartOptions: any;

  // Palette moderne harmonisée
  readonly dashboardColors = ['#1e3a8a', '#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6'];

  constructor(private portefeuilleService: PortefeuilleService, private ordreService: OrdreService) { }

  getCurrentTime(): string {
    return new Date().toLocaleTimeString('fr-FR', {
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  getExecutedOrdersCount(): number {
    if (!this.kpiData || !this.kpiData.repartitionStatut) return 0;
    return this.kpiData.repartitionStatut['EXECUTE'] || 0;
  }

  getPendingOrdersCount(): number {
    if (!this.kpiData || !this.kpiData.repartitionStatut) return 0;
    return this.kpiData.repartitionStatut['EN_ATTENTE'] || 0;
  }

  getCancelledOrdersCount(): number {
    if (!this.kpiData || !this.kpiData.repartitionStatut) return 0;
    return this.kpiData.repartitionStatut['ANNULE'] || 0;
  }

  calculerRepartitionSecteurFrontend(): void {
    this.portefeuilleService.getPortefeuilles().subscribe({
      next: (portefeuilles) => {
        // Calculer la répartition par secteur basée sur la valeur
        const repartitionParSecteur: { [secteur: string]: number } = {};
        let valeurTotale = 0;

        portefeuilles.forEach(p => {
          const secteur = p.titreSecteur;
          const valeur = Math.abs(p.valeur); // Utiliser valeur absolue pour éviter les valeurs négatives

          if (!repartitionParSecteur[secteur]) {
            repartitionParSecteur[secteur] = 0;
          }
          repartitionParSecteur[secteur] += valeur;
          valeurTotale += valeur;
        });

        // Calculer les pourcentages
        const repartitionPourcent: { [secteur: string]: number } = {};
        Object.keys(repartitionParSecteur).forEach(secteur => {
          repartitionPourcent[secteur] = valeurTotale > 0
            ? (repartitionParSecteur[secteur] / valeurTotale) * 100
            : 0;
        });

        console.log('✅ Répartition calculée côté frontend:', repartitionPourcent);

        // Créer le graphique avec les données calculées
        this.pieChartData = {
          labels: Object.keys(repartitionPourcent),
          datasets: [{
            data: Object.values(repartitionPourcent),
            backgroundColor: this.dashboardColors.slice(0, Object.keys(repartitionPourcent).length)
          }]
        };
      },
      error: (err) => {
        console.error('Erreur lors du calcul de répartition:', err);
        // Fallback avec des données d'exemple
        this.creerDonneesExemple();
      }
    });
  }

  creerDonneesExemple(): void {
    console.log('📊 Création de données d\'exemple pour la répartition par secteur');
    const donneesExemple = {
      'Technologie': 45,
      'Finance': 25,
      'Santé': 20,
      'Industrie': 10
    };

    this.pieChartData = {
      labels: Object.keys(donneesExemple),
      datasets: [{
        data: Object.values(donneesExemple),
        backgroundColor: this.dashboardColors.slice(0, Object.keys(donneesExemple).length)
      }]
    };
  }

  ngOnInit(): void {
    // Debug: Vérifier les portefeuilles individuels pour voir les secteurs
    this.portefeuilleService.getPortefeuilles().subscribe({
      next: (portefeuilles) => {
        console.log('🔍 Portefeuilles individuels:', portefeuilles);
        const secteurs = portefeuilles.map(p => p.titreSecteur);
        console.log('📊 Secteurs trouvés dans les portefeuilles:', [...new Set(secteurs)]);
      },
      error: (err) => console.error('Erreur portefeuilles:', err)
    });

    this.ordreService.getOrdreKpi('2025-01-01', '2025-12-31').subscribe({
      next: (data) => {
        this.kpiData = data;

        this.typeLabels = Object.keys(data.repartitionType);
        this.typeValues = Object.values(data.repartitionType);

        this.statutLabels = Object.keys(data.repartitionStatut);
        this.statutValues = Object.values(data.repartitionStatut);
        this.typeChartData = {
          labels: this.typeLabels,
          datasets: [{
            data: this.typeValues,
            backgroundColor: this.dashboardColors.slice(0, this.typeValues.length)
          }]
        };

        this.statutChartData = {
          labels: this.statutLabels,
          datasets: [
            {
              label: 'Ordres',
              data: this.statutValues,
              backgroundColor: this.dashboardColors.slice(0, this.statutValues.length)
            }
          ]
        };


      },
      error: (err) => console.error('Erreur de chargement KPI ordres', err)
    });

    this.portefeuilleService.getKpi().subscribe((data) => {
      this.kpi = data;

      // Debug pour voir les données reçues
      console.log('🔍 Données KPI reçues:', data);
      console.log('📊 Répartition par secteur:', data.repartitionParSecteur);
      console.log('📊 Répartition par secteur (%):', data.repartitionParSecteurPourcent);

      // Vérifier si les données de secteur sont vides ou incomplètes
      const secteurKeys = Object.keys(data.repartitionParSecteurPourcent);
      if (secteurKeys.length <= 1) {
        console.log('⚠️ Données de secteur insuffisantes, calcul côté frontend...');
        this.calculerRepartitionSecteurFrontend();
      } else {
        this.pieChartData = {
          labels: Object.keys(data.repartitionParSecteurPourcent),
          datasets: [{
            data: Object.values(data.repartitionParSecteurPourcent),
            backgroundColor: this.dashboardColors.slice(0, Object.keys(data.repartitionParSecteurPourcent).length)
          }]
        };
      }

      this.barChartData = {
        labels: Object.keys(data.repartitionParSecteur),
        datasets: [{
          label: 'Répartition',
          data: Object.values(data.repartitionParSecteur),
          backgroundColor: this.dashboardColors.slice(0, Object.keys(data.repartitionParSecteur).length)
        }]
      };

      this.pieChartOptions = {
        responsive: true,
        plugins: {
          legend: {
            position: 'bottom',
            labels: {
              color: '#495057'
            }
          }
        }
      };

      this.barChartOptions = {
        responsive: true,
        scales: {
          x: {
            ticks: { color: '#495057' },
            grid: { color: '#ebedef' }
          },
          y: {
            beginAtZero: true,
            ticks: { color: '#495057' },
            grid: { color: '#ebedef' }
          }
        },
        plugins: {
          legend: { display: false }
        }
      };

    });
  }
}