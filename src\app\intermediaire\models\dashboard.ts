export interface HistoriqueExecutionDTO {
    operationId: string;
    date: string;
    nbOrdres: number;
    montant: number;
    statut: string;
}

export interface NotificationRequestDTO {
    message: string;
    destinataire?: string;
    type?: string;
}

export interface DashboardKpiDTO {
    topActionsEchangees: Array<{ [key: string]: any }>;
    ordresNonExecutables: number;
    commissionDerniers2Jours: number;
    ratioExecutionSurValides: number;
    croissanceVolumeOperations: number;
    investisseursActifsAujourdhui: number;
}

export interface ActionDTO {
    isin: string;
    symbole: string;
    nom: string;
    secteur: string;
    dernierPrix: number;
    quantiteDispo: number;
    description: string;
    date: string;
    emetteurNom: string;
    prixList: PrixDTO[];
}

export interface PrixDTO {
    prixActuel: number;
    prixOuverture: number;
    prixCloture: number;
    date: string;
}
