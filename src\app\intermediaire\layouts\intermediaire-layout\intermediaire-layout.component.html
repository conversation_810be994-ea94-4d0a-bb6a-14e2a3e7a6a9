<div class="layout">
    <aside class="sidebar" [class.collapsed]="sidebarCollapsed">
        <div class="sidebar-header">
            <div class="logo">
                <div class="logo-icon">
                    <i class="pi pi-chart-line"></i>
                </div>
                <div class="logo-text" *ngIf="!sidebarCollapsed">
                    <span class="brand-name">InterBourse</span>
                    <span class="brand-subtitle">Intermédiaire</span>
                </div>
            </div>
            <button class="sidebar-toggle" (click)="toggleSidebar()">
                <i class="pi" [ngClass]="sidebarCollapsed ? 'pi-angle-right' : 'pi-angle-left'"></i>
            </button>
        </div>

        <nav class="nav-menu">
            <div class="nav-section">
                <div class="nav-section-title" *ngIf="!sidebarCollapsed">Principal</div>
                <ul>
                    <li [ngClass]="{ active: isActiveRoute('dashboard') }">
                        <button (click)="navigateTo('dashboard')" [title]="sidebarCollapsed ? 'Dashboard' : ''">
                            <div class="nav-icon">
                                <i class="pi pi-home"></i>
                            </div>
                            <span class="nav-text" *ngIf="!sidebarCollapsed">Dashboard</span>
                            <div class="nav-indicator" *ngIf="isActiveRoute('dashboard')"></div>
                        </button>
                    </li>
                    <li routerLinkActive="active">
                        <button (click)="navigateTo('clients')" [title]="sidebarCollapsed ? 'Clients' : ''">
                            <div class="nav-icon">
                                <i class="pi pi-users"></i>
                            </div>
                            <span class="nav-text" *ngIf="!sidebarCollapsed">Demandes d'ordres</span>
                            <div class="nav-indicator"></div>
                        </button>
                    </li>
                    <li routerLinkActive="active">
                        <button (click)="navigateTo('ordre')" [title]="sidebarCollapsed ? 'Ordres' : ''">
                            <div class="nav-icon">
                                <i class="pi pi-chart-bar"></i>
                            </div>
                            <span class="nav-text" *ngIf="!sidebarCollapsed">Ordres</span>
                            <div class="nav-indicator"></div>
                        </button>
                    </li>
                     <li routerLinkActive="active">
                        <button (click)="navigateTo('operations')" [title]="sidebarCollapsed ? 'Opérations' : ''">
                            <div class="nav-icon">
                                <i class="pi pi-send"></i>
                            </div>
                            <span class="nav-text" *ngIf="!sidebarCollapsed">Opérations</span>
                            <div class="nav-indicator"></div>
                        </button>
                    </li>
                </ul>
            </div>

            
        </nav>

        <div class="sidebar-footer" *ngIf="!sidebarCollapsed">
            <div class="user-profile">
                <div class="user-avatar">
                    <i class="pi pi-user"></i>
                </div>
                <div class="user-info">
                    <div class="user-name">Intermédiaire</div>
                    <div class="user-role">En bourse</div>
                </div>
            </div>
        </div>
    </aside>

    <div class="main-content">
        <header class="navbar">
            <div class="navbar-left">
                <div class="breadcrumb">
                    <i class="pi pi-home"></i>
                    <span class="breadcrumb-separator">/</span>
                    <span class="current-page">{{ getCurrentPageTitle() }}</span>
                </div>
            </div>

            <div class="navbar-center">
                <div class="search-container">
                    <i class="pi pi-search search-icon"></i>
                    <input type="text" placeholder="Rechercher..." class="search-input" />
                </div>
            </div>

            <div class="navbar-right">
                <div class="navbar-actions">
                    <button class="action-btn notification-btn" (click)="navigateTo('notifications')"
                        [title]="'Notifications'">
                        <i class="pi pi-bell"></i>
                        <span class="notification-dot" *ngIf="notificationCount > 0"></span>
                    </button>

                    <div class="user-menu" (click)="toggleUserMenu()">
                        <div class="user-avatar-nav">
                            <i class="pi pi-user"></i>
                        </div>
                        <div class="user-info-nav" *ngIf="!sidebarCollapsed">
                            <span class="user-name-nav">Admin</span>
                            <i class="pi pi-angle-down dropdown-icon"></i>
                        </div>

                        <div class="user-dropdown" *ngIf="userMenuOpen">
                            <div class="dropdown-item" (click)="navigateTo('parametres')">
                                <i class="pi pi-cog"></i>
                                <span>Paramètres</span>
                            </div>
                            <div class="dropdown-item" (click)="navigateTo('profile')">
                                <i class="pi pi-user"></i>
                                <span>Profil</span>
                            </div>
                            <div class="dropdown-divider"></div>
                            <div class="dropdown-item logout-item" (click)="logout()">
                                <i class="pi pi-sign-out"></i>
                                <span>Déconnexion</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </header>

        <main class="content">
            <router-outlet></router-outlet>
        </main>
    </div>
</div>