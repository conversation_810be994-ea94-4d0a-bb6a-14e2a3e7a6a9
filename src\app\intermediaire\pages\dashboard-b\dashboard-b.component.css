/* ===== DESIGN ULTRA-MODERNE DASHBOARD INTERMÉDIAIRE ===== */

/* Variables CSS pour la cohérence */
:root {
    --primary-color: #0d542c;
    --primary-light: #118749;
    --primary-dark: #0a3d20;
    --secondary-color: #f8f9fa;
    --accent-color: #e3f2fd;
    --text-primary: #2c3e50;
    --text-secondary: #6c757d;
    --text-light: #95a5a6;
    --border-color: #e9ecef;
    --shadow-light: 0 2px 8px rgba(0, 0, 0, 0.08);
    --shadow-medium: 0 4px 16px rgba(0, 0, 0, 0.12);
    --shadow-heavy: 0 8px 32px rgba(0, 0, 0, 0.16);
    --border-radius: 12px;
    --border-radius-lg: 16px;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --gradient-primary: linear-gradient(135deg, #0d542c 0%, #118749 100%);
    --gradient-success: linear-gradient(135deg, #10b981 0%, #059669 100%);
    --gradient-warning: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    --gradient-danger: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    --gradient-info: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
}

/* Container principal ultra-moderne */
.ultra-modern-dashboard {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    min-height: 100vh;
    width: 100%;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    position: relative;
    overflow-x: hidden;
}

/* ===== HEADER DASHBOARD ===== */
.dashboard-header {
    background: linear-gradient(135deg, #0d542c 0%, #118749 100%);

    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    padding: 2rem 2.5rem;
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
}

.title-section {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.dashboard-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2c3e50;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 1rem;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.title-icon {
    font-size: 2.5rem;
    color: #e9ecef;
    -webkit-text-fill-color: #e9ecef;
}

.dashboard-subtitle {
    font-size: 1.1rem;
    color: #e9ecef;
    margin: 0;
    font-weight: 500;
}

.header-actions {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.modern-action-btn {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.875rem 1.5rem;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: 1px solid #e9ecef;
    border-radius: 12px;
    color: #2c3e50;
    font-weight: 600;
    font-size: 0.95rem;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    position: relative;
    overflow: hidden;
}

.modern-action-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.5s;
}

.modern-action-btn:hover::before {
    left: 100%;
}

.modern-action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
    border-color: #118749;
}

.refresh-btn:hover {
    background: linear-gradient(135deg, #0d542c 0%, #118749 100%);
    color: white;
}

.notification-btn:hover {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    color: white;
}

.modern-action-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.modern-action-btn i {
    font-size: 1.1rem;
}

/* ===== LOADING OVERLAY ===== */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.loading-content {
    text-align: center;
    padding: 2rem;
}

.loading-spinner {
    width: 60px;
    height: 60px;
    border: 4px solid #e9ecef;
    border-top: 4px solid #0d542c;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

.loading-text {
    font-size: 1.1rem;
    color: #6c757d;
    font-weight: 500;
    margin: 0;
}

/* ===== CONTENU PRINCIPAL ===== */
.dashboard-content {
    padding: 2.5rem;
    max-width: 1400px;
    margin: 0 auto;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.dashboard-content.loading {
    opacity: 0.7;
    pointer-events: none;
}

/* ===== SECTION TITLES ===== */
.section-title {
    font-size: 1.75rem;
    font-weight: 700;
    color: #2c3e50;
    margin: 0 0 2rem 0;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.section-title i {
    font-size: 1.5rem;
    color: #0d542c;
}

/* ===== KPI SECTION ===== */
.kpi-section {
    margin-bottom: 3rem;
}

.kpi-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-top: 1.5rem;
}

/* ===== KPI CARDS ===== */
.kpi-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    padding: 1.75rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    cursor: pointer;
}

.kpi-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, #0d542c 0%, #118749 100%);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.kpi-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.16);
}

.kpi-card:hover::before {
    height: 6px;
}

/* Variantes de couleurs pour les KPI cards */
.danger-card::before {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
}

.success-card::before {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.info-card::before {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
}

.warning-card::before {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.primary-card::before {
    background: linear-gradient(135deg, #0d542c 0%, #118749 100%);
}

.info-card::before {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.icon-container {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, rgba(13, 84, 44, 0.1) 0%, rgba(17, 135, 73, 0.1) 100%);
    color: #0d542c;
    font-size: 1.5rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.danger-card .icon-container {
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.1) 0%, rgba(220, 38, 38, 0.1) 100%);
    color: #ef4444;
}

.success-card .icon-container {
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(5, 150, 105, 0.1) 100%);
    color: #10b981;
}

.info-card .icon-container {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(37, 99, 235, 0.1) 100%);
    color: #3b82f6;
}

.warning-card .icon-container {
    background: linear-gradient(135deg, rgba(245, 158, 11, 0.1) 0%, rgba(217, 119, 6, 0.1) 100%);
    color: #f59e0b;
}

.kpi-card:hover .icon-container {
    transform: scale(1.1);
}

.trend-indicator {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 0.75rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 600;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.trend-indicator.positive {
    background: rgba(16, 185, 129, 0.1);
    color: #059669;
}

.trend-indicator.negative {
    background: rgba(239, 68, 68, 0.1);
    color: #dc2626;
}

.trend-indicator.neutral {
    background: rgba(107, 114, 128, 0.1);
    color: #6b7280;
}

.trend-positive {
    background: rgba(16, 185, 129, 0.1);
    color: #059669;
}

.trend-negative {
    background: rgba(239, 68, 68, 0.1);
    color: #dc2626;
}

.trend-neutral {
    background: rgba(107, 114, 128, 0.1);
    color: #6b7280;
}

.card-content {
    margin-bottom: 1.5rem;
}

.kpi-label {
    font-size: 1rem;
    font-weight: 600;
    color: #6c757d;
    margin: 0 0 0.75rem 0;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.kpi-value-container {
    display: flex;
    align-items: baseline;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
}

.kpi-value {
    font-size: 2.25rem;
    font-weight: 800;
    color: #2c3e50;
    line-height: 1;
}

.kpi-unit,
.currency {
    font-size: 1rem;
    font-weight: 600;
    color: #6c757d;
}

.kpi-subtitle {
    font-size: 0.875rem;
    color: #95a5a6;
    font-weight: 500;
}

.card-footer {
    margin-top: auto;
}

.progress-bar {
    width: 100%;
    height: 6px;
    background: rgba(0, 0, 0, 0.1);
    border-radius: 3px;
    overflow: hidden;
    position: relative;
}

.progress-fill {
    height: 100%;
    border-radius: 3px;
    transition: width 1s ease-in-out;
    position: relative;
    overflow: hidden;
}

.progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% {
        left: -100%;
    }

    100% {
        left: 100%;
    }
}

.primary-fill {
    background: linear-gradient(135deg, #0d542c 0%, #118749 100%);
}

.success-fill {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.warning-fill {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.danger-fill {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
}

.info-fill {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
}

/* ===== SECTION GRAPHIQUES ===== */
.charts-section {
    margin-bottom: 3rem;
}

.charts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
    margin-top: 1.5rem;
}

.chart-container {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    padding: 1.75rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.chart-container:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #e9ecef;
}

.chart-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: #2c3e50;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.chart-title i {
    color: #0d542c;
}

.chart-actions {
    display: flex;
    gap: 0.5rem;
}

.chart-action-btn {
    width: 40px;
    height: 40px;
    border: 1px solid #e9ecef;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    color: #6c757d;
}

.chart-action-btn:hover {
    background: #0d542c;
    color: white;
    transform: scale(1.1);
}

.chart-content {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 300px;
}

/* ===== SECTION HISTORIQUE ===== */
.historique-section {
    margin-bottom: 2rem;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.section-actions {
    display: flex;
    gap: 1rem;
}



.table-container {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    padding: 1.5rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    overflow: hidden;
}

/* ===== STYLES TABLE MODERNE ===== */
::ng-deep .modern-table {
    border-radius: 12px;
    overflow: hidden;
}

::ng-deep .modern-table .p-datatable-header {
    background: linear-gradient(135deg, #0d542c 0%, #118749 100%);
    color: white;
    border: none;
    padding: 1rem;
}

::ng-deep .modern-table .p-datatable-thead>tr>th {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: none;
    padding: 1rem;
    font-weight: 600;
    color: #2c3e50;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.875rem;
}

::ng-deep .modern-table .p-datatable-tbody>tr {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

::ng-deep .modern-table .p-datatable-tbody>tr:hover {
    background: rgba(13, 84, 44, 0.05);
    transform: scale(1.01);
}

::ng-deep .modern-table .p-datatable-tbody>tr>td {
    border: none;
    padding: 1rem;
    border-bottom: 1px solid #e9ecef;
}

.date-cell {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #6c757d;
    font-weight: 500;
}

.date-cell i {
    color: #0d542c;
}

.number-cell {
    text-align: center;
}

.number-badge {
    background: linear-gradient(135deg, #0d542c 0%, #118749 100%);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.875rem;
}

.amount-cell {
    text-align: right;
}

.amount-value {
    font-weight: 700;
    color: #2c3e50;
    font-size: 1.1rem;
}

.action-buttons {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
    
}

.action-btn {
    width: 36px;
    height: 36px;
    border: 1px solid #e9ecef;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    color: #6c757d;
}

.view-btn:hover {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    color: white;
    transform: scale(1.1);
    border-radius: 5px;
    border: none;
}

.download-btn:hover {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    transform: scale(1.1);
}

.empty-message {
    text-align: center;
    padding: 3rem 1rem;
}

.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
    color: #95a5a6;
}

.empty-state i {
    font-size: 3rem;
    color: #95a5a6;
}

.empty-state p {
    font-size: 1.1rem;
    margin: 0;
}

/* ===== DIALOG NOTIFICATION ===== */
::ng-deep .modern-dialog {
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.16);
}

::ng-deep .modern-dialog .p-dialog-header {
    background: linear-gradient(135deg, #0d542c 0%, #118749 100%);
    color: white;
    padding: 1.5rem;
    border: none;
}

::ng-deep .modern-dialog .p-dialog-content {
    padding: 2rem;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%);
}

::ng-deep .modern-dialog .p-dialog-footer {
    background: rgba(248, 250, 252, 0.95);
    border: none;
    padding: 1.5rem 2rem;
}

.notification-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.form-label {
    font-weight: 600;
    color: #2c3e50;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.95rem;
}

.form-label i {
    color: #0d542c;
}

.form-input,
.form-textarea {
    padding: 0.875rem 1rem;
    border: 1px solid #e9ecef;
    border-radius: 12px;
    font-size: 0.95rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    background: rgba(255, 255, 255, 0.9);
}

.form-input:focus,
.form-textarea:focus {
    outline: none;
    border-color: #0d542c;
    box-shadow: 0 0 0 3px rgba(13, 84, 44, 0.1);
}

.dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
}

/* ===== ANIMATIONS SUPPLÉMENTAIRES ===== */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }

    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }

    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.kpi-card {
    animation: fadeInUp 0.6s ease-out;
}

.kpi-card:nth-child(1) {
    animation-delay: 0.1s;
}

.kpi-card:nth-child(2) {
    animation-delay: 0.2s;
}

.kpi-card:nth-child(3) {
    animation-delay: 0.3s;
}

.kpi-card:nth-child(4) {
    animation-delay: 0.4s;
}

.kpi-card:nth-child(5) {
    animation-delay: 0.5s;
}

.chart-container:nth-child(1) {
    animation: slideInLeft 0.8s ease-out;
}

.chart-container:nth-child(2) {
    animation: slideInRight 0.8s ease-out;
}

/* ===== DESIGN RESPONSIF ===== */

/* Large Desktop (1400px+) */
@media (min-width: 1400px) {
    .ultra-modern-dashboard {
        max-width: 1400px;
        margin: 0 auto;
    }

    .dashboard-content {
        padding: 3rem;
    }

    .kpi-grid {
        grid-template-columns: repeat(5, 1fr);
    }
}

/* Desktop (1200px - 1399px) */
@media (max-width: 1399px) {
    .kpi-grid {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    }

    .charts-grid {
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    }
}

/* Tablette Large (992px - 1199px) */
@media (max-width: 1199px) {
    .dashboard-header {
        padding: 1.5rem 2rem;
    }

    .header-content {
        flex-direction: column;
        gap: 1.5rem;
        text-align: center;
    }

    .dashboard-content {
        padding: 2rem;
    }

    .kpi-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1rem;
    }

    .charts-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
}

/* Tablette (768px - 991px) */
@media (max-width: 991px) {
    .dashboard-title {
        font-size: 2rem;
    }

    .title-icon {
        font-size: 2rem;
    }

    .header-actions {
        flex-direction: column;
        width: 100%;
        gap: 0.75rem;
    }

    .modern-action-btn {
        width: 100%;
        justify-content: center;
    }

    .section-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .section-actions {
        width: 100%;
        justify-content: center;
    }
}

/* Mobile Large (576px - 767px) */
@media (max-width: 767px) {
    .dashboard-header {
        padding: 1rem;
    }

    .dashboard-content {
        padding: 1rem;
    }

    .dashboard-title {
        font-size: 1.75rem;
        flex-direction: column;
        gap: 0.5rem;
    }

    .kpi-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .kpi-card {
        padding: 1.25rem;
    }

    .kpi-value {
        font-size: 1.875rem;
    }

    .chart-container {
        padding: 1rem;
    }

    .chart-content {
        min-height: 250px;
    }

    .table-container {
        padding: 1rem;
        overflow-x: auto;
    }

    ::ng-deep .modern-table .p-datatable-thead>tr>th,
    ::ng-deep .modern-table .p-datatable-tbody>tr>td {
        padding: 0.75rem 0.5rem;
        font-size: 0.875rem;
    }

    .action-buttons {
        flex-direction: column;
        gap: 0.25rem;
    }

    .action-btn {
        width: 32px;
        height: 32px;
    }
}

/* Mobile Small (≤575px) */
@media (max-width: 575px) {
    .dashboard-header {
        padding: 0.75rem;
    }

    .dashboard-content {
        padding: 0.75rem;
    }

    .dashboard-title {
        font-size: 1.5rem;
    }

    .title-icon {
        font-size: 1.5rem;
    }

    .kpi-card {
        padding: 1rem;
    }

    .card-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .icon-container {
        width: 50px;
        height: 50px;
        font-size: 1.25rem;
    }

    .kpi-value {
        font-size: 1.5rem;
    }

    .chart-container {
        padding: 0.75rem;
    }

    .chart-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .chart-title {
        font-size: 1rem;
    }

    .section-title {
        font-size: 1.5rem;
        flex-direction: column;
        gap: 0.5rem;
        text-align: center;
    }

    ::ng-deep .modern-dialog {
        width: 95vw !important;
        max-width: none !important;
    }

    .notification-form {
        gap: 1rem;
    }

    .dialog-footer {
        flex-direction: column;
        gap: 0.75rem;
    }

    .dialog-footer button {
        width: 100%;
    }
}

/* Amélioration de l'accessibilité */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .loading-spinner {
        animation: none;
    }

    .progress-fill::after {
        animation: none;
    }
}

/* Mode sombre (optionnel) */
@media (prefers-color-scheme: dark) {
    :root {
        --primary-color: #118749;
        --primary-light: #1ea55c;
        --primary-dark: #0d542c;
        --secondary-color: #2c3e50;
        --accent-color: #34495e;
        --text-primary: #ecf0f1;
        --text-secondary: #bdc3c7;
        --text-light: #95a5a6;
        --border-color: #34495e;
    }

    .ultra-modern-dashboard {
        background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    }

    .dashboard-header {
        background: linear-gradient(135deg, rgba(44, 62, 80, 0.95) 0%, rgba(52, 73, 94, 0.95) 100%);
        border-bottom-color: rgba(255, 255, 255, 0.1);
    }

    .kpi-card,
    .chart-container,
    .table-container {
        background: linear-gradient(135deg, rgba(44, 62, 80, 0.95) 0%, rgba(52, 73, 94, 0.95) 100%);
        border-color: rgba(255, 255, 255, 0.1);
    }
}

/* ===== SECTION ACTIONS ===== */
.actions-section {
    margin-bottom: 3rem;
}

.actions-stats {
    display: flex;
    gap: 2rem;
    align-items: center;
}

.stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.25rem;
}

.stat-value {
    font-size: 1.5rem;
    font-weight: 800;
    color: #0d542c;
    line-height: 1;
}

.stat-label {
    font-size: 0.75rem;
    font-weight: 600;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.actions-table-container {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    padding: 1.5rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    overflow: hidden;
}

/* ===== STYLES TABLE ACTIONS MODERNE ===== */
::ng-deep .modern-actions-table {
    border-radius: 12px;
    overflow: hidden;
}

::ng-deep .modern-actions-table .p-datatable-header {
    background: linear-gradient(135deg, #0d542c 0%, #118749 100%);
    color: white;
    border: none;
    padding: 1rem;
}

::ng-deep .modern-actions-table .p-datatable-thead>tr>th {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: none;
    padding: 1rem 0.75rem;
    font-weight: 600;
    color: #2c3e50;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.875rem;
}

::ng-deep .modern-actions-table .p-datatable-tbody>tr {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

::ng-deep .modern-actions-table .p-datatable-tbody>tr:hover {
    background: rgba(13, 84, 44, 0.05);
    transform: scale(1.005);
}

::ng-deep .modern-actions-table .p-datatable-tbody>tr>td {
    border: none;
    padding: 1rem 0.75rem;
    border-bottom: 1px solid #e9ecef;
    vertical-align: middle;
}

/* Colonnes spécifiques */
.col-avatar {
    width: 60px;
    text-align: center;
}

.col-name {
    min-width: 200px;
}

.col-isin {
    width: 120px;
}

.col-sector {
    width: 150px;
}

.col-price {
    width: 140px;
}

.col-quantity {
    width: 100px;
    text-align: center;
}

.col-date {
    width: 120px;
}

.col-actions {
    width: 140px;
    text-align: center;
}

/* Avatar des actions */
.action-avatar {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 1.1rem;
    color: white;
    margin: 0 auto;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.action-avatar:hover {
    transform: scale(1.1);
}

.avatar-blue {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
}

.avatar-green {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.avatar-purple {
    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
}

.avatar-orange {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.avatar-red {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
}

/* Informations de l'action */
.action-info {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.action-name {
    font-weight: 700;
    color: #2c3e50;
    font-size: 1rem;
    line-height: 1.2;
}

.action-symbol {
    font-size: 0.875rem;
    color: #6c757d;
    font-weight: 600;
    font-family: 'Courier New', monospace;
}

.action-emetteur {
    font-size: 0.75rem;
    color: #95a5a6;
    font-style: italic;
}

/* Badge ISIN */
.isin-badge {
    background: linear-gradient(135deg, rgba(13, 84, 44, 0.1) 0%, rgba(17, 135, 73, 0.1) 100%);
    color: #0d542c;
    padding: 0.375rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    font-family: 'Courier New', monospace;
    border: 1px solid rgba(13, 84, 44, 0.2);
}

/* Badges secteur */
.sector-badge {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 0.75rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 600;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.sector-tech {
    background: rgba(59, 130, 246, 0.1);
    color: #2563eb;
    border: 1px solid rgba(59, 130, 246, 0.2);
}

.sector-finance {
    background: rgba(16, 185, 129, 0.1);
    color: #059669;
    border: 1px solid rgba(16, 185, 129, 0.2);
}

.sector-industry {
    background: rgba(107, 114, 128, 0.1);
    color: #4b5563;
    border: 1px solid rgba(107, 114, 128, 0.2);
}

.sector-ecommerce {
    background: rgba(245, 158, 11, 0.1);
    color: #d97706;
    border: 1px solid rgba(245, 158, 11, 0.2);
}

.sector-bank {
    background: rgba(16, 185, 129, 0.1);
    color: #059669;
    border: 1px solid rgba(16, 185, 129, 0.2);
}

.sector-insurance {
    background: rgba(139, 92, 246, 0.1);
    color: #7c3aed;
    border: 1px solid rgba(139, 92, 246, 0.2);
}

.sector-real-estate {
    background: rgba(245, 158, 11, 0.1);
    color: #d97706;
    border: 1px solid rgba(245, 158, 11, 0.2);
}

.sector-energy {
    background: rgba(239, 68, 68, 0.1);
    color: #dc2626;
    border: 1px solid rgba(239, 68, 68, 0.2);
}

.sector-telecom {
    background: rgba(59, 130, 246, 0.1);
    color: #2563eb;
    border: 1px solid rgba(59, 130, 246, 0.2);
}

.sector-health {
    background: rgba(16, 185, 129, 0.1);
    color: #059669;
    border: 1px solid rgba(16, 185, 129, 0.2);
}

.sector-default {
    background: rgba(107, 114, 128, 0.1);
    color: #6b7280;
    border: 1px solid rgba(107, 114, 128, 0.2);
}

/* Container prix */
.price-container {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    align-items: flex-end;
}

.price-value {
    font-weight: 700;
    color: #2c3e50;
    font-size: 1.1rem;
}

.price-change {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
}

.price-change.positive {
    background: rgba(16, 185, 129, 0.1);
    color: #059669;
}

.price-change.negative {
    background: rgba(239, 68, 68, 0.1);
    color: #dc2626;
}

/* Badge quantité */
.quantity-cell {
    text-align: center;
}

.quantity-badge {
    background: linear-gradient(135deg, #0d542c 0%, #118749 100%);
    color: white;
    padding: 0.375rem 0.75rem;
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.875rem;
}

/* Boutons d'action supplémentaires */
.info-btn:hover {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    color: white;
    transform: scale(1.1);
}

.chart-btn:hover {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    color: white;
    transform: scale(1.1);
}

/* Animation des lignes */
.action-row {
    animation: fadeInUp 0.6s ease-out both;
}

/* Responsive pour les actions */
@media (max-width: 1199px) {
    .actions-stats {
        gap: 1rem;
    }

    .stat-value {
        font-size: 1.25rem;
    }
}

@media (max-width: 767px) {
    .actions-stats {
        flex-direction: column;
        gap: 0.75rem;
    }

    .actions-table-container {
        padding: 1rem;
        overflow-x: auto;
    }

    ::ng-deep .modern-actions-table .p-datatable-thead>tr>th,
    ::ng-deep .modern-actions-table .p-datatable-tbody>tr>td {
        padding: 0.75rem 0.5rem;
        font-size: 0.875rem;
    }

    .action-info {
        gap: 0.125rem;
    }

    .action-name {
        font-size: 0.875rem;
    }

    .action-symbol,
    .action-emetteur {
        font-size: 0.75rem;
    }

    .table-actions {
        flex-direction: column;
        gap: 0.25rem;
    }

    .table-action-btn {
        width: 32px;
        height: 32px;
    }
}

/* ===== DIALOG DÉTAILS ACTION ===== */
::ng-deep .modern-action-dialog {
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.16);
}

::ng-deep .modern-action-dialog .p-dialog-header {
    background: linear-gradient(135deg, #0d542c 0%, #118749 100%);
    color: white;
    padding: 1.5rem 2rem;
    border: none;
}

::ng-deep .modern-action-dialog .p-dialog-content {
    padding: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%);
    max-height: 70vh;
    overflow-y: auto;
}

::ng-deep .modern-action-dialog .p-dialog-footer {
    background: rgba(248, 250, 252, 0.95);
    border: none;
    padding: 1.5rem 2rem;
}

.action-details-content {
    padding: 2rem;
}

/* ===== HEADER ACTION ===== */
.action-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
    border-bottom: 2px solid #e9ecef;
}

.action-main-info {
    display: flex;
    gap: 1.5rem;
    align-items: flex-start;
}

.action-avatar-large {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 2rem;
    color: white;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.action-title-section {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.action-title {
    font-size: 2rem;
    font-weight: 800;
    color: #2c3e50;
    margin: 0;
    line-height: 1.2;
}

.action-subtitle {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 1.1rem;
    color: #6c757d;
}

.action-symbol-large {
    font-family: 'Courier New', monospace;
    font-weight: 700;
    color: #0d542c;
}

.action-separator {
    color: #95a5a6;
}

.action-emetteur-large {
    font-style: italic;
    color: #6c757d;
}

.action-isin-large {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.95rem;
    color: #95a5a6;
    font-family: 'Courier New', monospace;
}

.action-isin-large i {
    color: #0d542c;
}

/* ===== SECTION PRIX ===== */
.action-price-section {
    text-align: right;
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.current-price {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.price-label {
    font-size: 0.875rem;
    color: #6c757d;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.price-value-large {
    font-size: 2.5rem;
    font-weight: 800;
    color: #2c3e50;
    line-height: 1;
}

.price-variation {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-weight: 600;
    font-size: 0.95rem;
}

.price-variation.positive {
    background: rgba(16, 185, 129, 0.1);
    color: #059669;
    border: 1px solid rgba(16, 185, 129, 0.2);
}

.price-variation.negative {
    background: rgba(239, 68, 68, 0.1);
    color: #dc2626;
    border: 1px solid rgba(239, 68, 68, 0.2);
}

.price-variation.neutral {
    background: rgba(107, 114, 128, 0.1);
    color: #6b7280;
    border: 1px solid rgba(107, 114, 128, 0.2);
}

/* ===== GRILLE INFORMATIONS ===== */
.action-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.info-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(248, 250, 252, 0.8) 100%);
    border: 1px solid #e9ecef;
    border-radius:  12px;
    padding: 1.5rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.info-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
}

.info-card.full-width {
    grid-column: 1 / -1;
}

.info-header {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1rem;
}

.info-header i {
    font-size: 1.25rem;
    color: #0d542c;
}

.info-header h3 {
    font-size: 1.1rem;
    font-weight: 700;
    color: #2c3e50;
    margin: 0;
}

.info-content {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.sector-badge-large {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    border-radius: 25px;
    font-size: 1rem;
    font-weight: 600;
}

.quantity-value-large {
    font-size: 1.75rem;
    font-weight: 800;
    color: #2c3e50;
}

.quantity-unit {
    font-size: 0.95rem;
    color: #6c757d;
    font-weight: 600;
}

.date-value {
    font-size: 1.1rem;
    font-weight: 600;
    color: #2c3e50;
}

.description-text {
    font-size: 1rem;
    line-height: 1.6;
    color: #6c757d;
    margin: 0;
}

/* ===== SECTION HISTORIQUE PRIX ===== */
.price-history-section {
    margin-top: 2rem;
}

.section-header-small {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #e9ecef;
}

.section-title-small {
    font-size: 1.5rem;
    font-weight: 700;
    color: #2c3e50;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.section-title-small i {
    color: #0d542c;
}

.price-count {
    background: linear-gradient(135deg, #0d542c 0%, #118749 100%);
    color: white;
    padding: 0.375rem 0.75rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 600;
}

.price-list-container {
    background: rgba(255, 255, 255, 0.8);
    border: 1px solid #e9ecef;
    border-radius:  12px;
    padding: 1rem;
    overflow: hidden;
}

.price-list-scroll {
    max-height: 400px;
    overflow-y: auto;
    padding-right: 0.5rem;
}

/* ===== ITEMS DE PRIX LISTE ===== */
.price-item {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.9) 100%);
    border: 1px solid #e9ecef;
    border-radius:  12px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    animation: fadeInUp 0.6s ease-out both;
    position: relative;
    overflow: hidden;
}

.price-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
    border-color: #0d542c;
}

.price-item:last-child {
    margin-bottom: 0;
}

/* ===== HEADER ITEM PRIX ===== */
.price-item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 0.75rem;
    border-bottom: 1px solid #e9ecef;
}

.price-date {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.price-date i {
    color: #0d542c;
    font-size: 1.1rem;
}

.date-text {
    font-weight: 700;
    color: #2c3e50;
    font-size: 1.1rem;
}

.date-time {
    color: #6c757d;
    font-size: 0.95rem;
    font-weight: 500;
    background: rgba(13, 84, 44, 0.1);
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
}

.price-variation-badge {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: 700;
    font-size: 0.95rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.price-variation-badge.positive {
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.15) 0%, rgba(16, 185, 129, 0.1) 100%);
    color: #059669;
    border: 1px solid rgba(16, 185, 129, 0.3);
}

.price-variation-badge.negative {
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.15) 0%, rgba(239, 68, 68, 0.1) 100%);
    color: #dc2626;
    border: 1px solid rgba(239, 68, 68, 0.3);
}

/* ===== DÉTAILS PRIX ===== */
.price-item-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
    margin-bottom: 1rem;
}

.price-detail-item {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    padding: 1rem;
    background: rgba(248, 250, 252, 0.6);
    border-radius:  12px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.price-detail-item:hover {
    background: rgba(248, 250, 252, 0.9);
    transform: scale(1.02);
}

.price-detail-item.current {
    background: linear-gradient(135deg, rgba(13, 84, 44, 0.1) 0%, rgba(17, 135, 73, 0.1) 100%);
    border: 1px solid rgba(13, 84, 44, 0.2);
}

.price-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    font-weight: 600;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.price-label i {
    color: #0d542c;
    font-size: 1rem;
}

.price-value {
    font-size: 1.25rem;
    font-weight: 800;
    color: #2c3e50;
    line-height: 1;
}

.price-detail-item.current .price-value {
    color: #0d542c;
}

/* ===== BARRE DE PROGRESSION ===== */
.price-progress-container {
    margin-top: 1rem;
}

.price-progress-bar {
    display: flex;
    height: 8px;
    background: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 0.5rem;
}

.progress-segment {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
}

.progress-segment.opening {
    background: linear-gradient(90deg, #6b7280 0%, #9ca3af 100%);
}

.progress-segment.current.positive {
    background: linear-gradient(90deg, #059669 0%, #10b981 100%);
}

.progress-segment.current.negative {
    background: linear-gradient(90deg, #dc2626 0%, #ef4444 100%);
}

.progress-segment.closing.positive {
    background: linear-gradient(90deg, #059669 0%, #10b981 100%);
}

.progress-segment.closing.negative {
    background: linear-gradient(90deg, #dc2626 0%, #ef4444 100%);
}

.progress-labels {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.progress-label {
    font-size: 0.75rem;
    color: #95a5a6;
    font-weight: 500;
}

/* ===== INFO LISTE ===== */
.price-list-info {
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid #e9ecef;
    text-align: center;
}

.price-info-text {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    color: #6c757d;
    font-size: 0.95rem;
    font-weight: 500;
}

.price-info-text i {
    color: #0d542c;
}

/* ===== ÉTATS VIDES ===== */
.empty-message-small {
    text-align: center;
    padding: 2rem 1rem;
}

.empty-state-small {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.75rem;
    color: #95a5a6;
}

.empty-state-small i {
    font-size: 2rem;
    color: #95a5a6;
}

.empty-state-small p {
    font-size: 1rem;
    margin: 0;
}

.no-price-history {
    margin-top: 2rem;
    text-align: center;
    padding: 3rem 2rem;
}

.empty-state-large {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
    color: #95a5a6;
}

.empty-state-large i {
    font-size: 4rem;
    color: #95a5a6;
}

.empty-state-large h4 {
    font-size: 1.5rem;
    font-weight: 700;
    color: #6c757d;
    margin: 0;
}

.empty-state-large p {
    font-size: 1.1rem;
    margin: 0;
    max-width: 400px;
    line-height: 1.5;
}

/* ===== FOOTER DIALOG ===== */
.dialog-footer-action {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
}

/* ===== RESPONSIVE POUR DIALOG ===== */
@media (max-width: 991px) {
    .action-header {
        flex-direction: column;
        gap: 1.5rem;
        text-align: center;
    }

    .action-price-section {
        text-align: center;
    }

    .action-info-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 767px) {
    ::ng-deep .modern-action-dialog {
        width: 95vw !important;
        max-width: none !important;
    }

    .action-details-content {
        padding: 1rem;
    }

    .action-main-info {
        flex-direction: column;
        align-items: center;
        text-align: center;
        gap: 1rem;
    }

    .action-avatar-large {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }

    .action-title {
        font-size: 1.5rem;
    }

    .price-value-large {
        font-size: 2rem;
    }

    .section-header-small {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .dialog-footer-action {
        flex-direction: column;
        gap: 0.75rem;
    }

    .dialog-footer-action button {
        width: 100%;
    }

    /* Responsive pour la liste des prix */
    .price-item {
        padding: 1rem;
    }

    .price-item-header {
        flex-direction: column;
        gap: 0.75rem;
        text-align: center;
    }

    .price-date {
        justify-content: center;
    }

    .price-item-details {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }

    .price-detail-item {
        padding: 0.75rem;
    }

    .price-value {
        font-size: 1.1rem;
    }

    .price-variation-badge {
        padding: 0.375rem 0.75rem;
        font-size: 0.875rem;
    }

    .progress-labels {
        font-size: 0.7rem;
    }
}