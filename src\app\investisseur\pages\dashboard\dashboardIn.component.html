<!-- En-tête du Dashboard -->
<div class="">
  <div class="dashboard-header ">
    <div class="header-content">
      <div class="welcome-section ">
        <h1 class="dashboard-title">
          <i class="pi pi-chart-line title-icon"></i>
          Dashboard Trading
        </h1>
        <p class="dashboard-subtitle">Vue d'ensemble de votre portefeuille et activités</p>
      </div>
      <div class="market-status-widget">
        <div class="market-indicator">
          <span class="market-dot"></span>
          <div class="market-info">
            <span class="market-label">Marché</span>
            <span class="market-status">Ouvert</span>
          </div>
        </div>
        <div class="market-time">
          <span class="time-label">Dernière mise à jour</span>
          <span class="time-value">{{ getCurrentTime() }}</span>
        </div>
      </div>
    </div>
  </div>

  <!-- Section KPI Dashboard Ultra-Moderne -->
  <div class="kpi-dashboard" *ngIf="kpi">
    <div class="kpi-grid">
      <!-- Balance Card -->
      <div class="kpi-card balance-card ">
        <div class="card-header">
          <div class="icon-container balance-icon">
            <i class="pi pi-wallet"></i>
          </div>
          <div class="trend-indicator positive">
            <i class="pi pi-arrow-up"></i>
            <span>+2.5%</span>
          </div>
        </div>
        <div class="card-content">
          <h3 class="kpi-label">Balance Disponible</h3>
          <div class="kpi-value-container">
            <span class="kpi-value">{{ kpi.soldeDisponible | number:'1.2-2' }}</span>
            <span class="currency">TND</span>
          </div>
          <div class="kpi-subtitle">Liquidités disponibles</div>
        </div>
        <div class="card-footer">
          <div class="progress-bar">
            <div class="progress-fill" style="width: 75%"></div>
          </div>
        </div>
      </div>

      <!-- Valeur Totale Card -->
      <div class="kpi-card portfolio-card ">
        <div class="card-header">
          <div class="icon-container portfolio-icon">
            <i class="pi pi-chart-line"></i>
          </div>
          <div class="trend-indicator positive">
            <i class="pi pi-arrow-up"></i>
            <span>+5.2%</span>
          </div>
        </div>
        <div class="card-content">
          <h3 class="kpi-label">Valeur Totale</h3>
          <div class="kpi-value-container">
            <span class="kpi-value">{{ kpi.valeurTotale | number:'1.2-2' }}</span>
            <span class="currency">TND</span>
          </div>
          <div class="kpi-subtitle">Portefeuille complet</div>
        </div>
        <div class="card-footer">
          <div class="mini-chart">
            <div class="chart-line"></div>
          </div>
        </div>
      </div>

      <!-- Nombre de Titres Card -->
      <div class="kpi-card assets-card ">
        <div class="card-header">
          <div class="icon-container assets-icon">
            <i class="pi pi-briefcase"></i>
          </div>
          <div class="asset-count-badge">{{ kpi.nombreTitres }}</div>
        </div>
        <div class="card-content">
          <h3 class="kpi-label">Actifs Détenus</h3>
          <div class="kpi-value-container">
            <span class="kpi-value">{{ kpi.nombreTitres }}</span>
            <span class="currency">Titres</span>
          </div>
          <div class="kpi-subtitle">Diversification active</div>
        </div>
        <div class="card-footer">
          <div class="asset-types">
            <span class="asset-type">Actions</span>
          </div>
        </div>
      </div>

      <!-- Total Ordres Card -->
      <div class="kpi-card orders-card ">
        <div class="card-header">
          <div class="icon-container orders-icon">
            <i class="pi pi-list"></i>
          </div>
          <div class="orders-status">
            <span class="status-dot active"></span>
            <span class="status-text">Actif</span>
          </div>
        </div>
        <div class="card-content">
          <h3 class="kpi-label">Total Ordres</h3>
          <div class="kpi-value-container">
            <span class="kpi-value">{{ kpiData.totalOrdres || 0 }}</span>
            <span class="currency">Ordres</span>
          </div>
          <div class="kpi-subtitle">Activité de trading</div>
        </div>
        <div class="card-footer">
          <div class="order-stats">
            <div class="stat-item">
              <span class="stat-label">Exécutés</span>
              <span class="stat-value">85%</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">En attente</span>
              <span class="stat-value">15%</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Taux d'annulation Card -->
      <div class="kpi-card performance-card ">
        <div class="card-header">
          <div class="icon-container performance-icon">
            <i class="pi pi-chart-pie"></i>
          </div>
          <div class="performance-badge">
            <span class="badge-text">Performance</span>
          </div>
        </div>
        <div class="card-content">
          <h3 class="kpi-label">Taux de Réussite</h3>
          <div class="kpi-value-container">
            <span class="kpi-value">{{ 100 - (kpiData.tauxAnnulation || 0) | number:'1.0-2' }}</span>
            <span class="currency">%</span>
          </div>
          <div class="kpi-subtitle">Ordres exécutés avec succès</div>
        </div>
        <div class="card-footer">
          <div class="performance-ring">
            <svg class="ring-svg" viewBox="0 0 36 36">
              <path class="ring-bg" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831">
              </path>
              <path class="ring-progress" [attr.stroke-dasharray]="(100 - (kpiData.tauxAnnulation || 0)) + ', 100'"
                d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"></path>
            </svg>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Section Analytics Ultra-Moderne -->
  <div class="analytics-section" *ngIf="pieChartData && barChartData">
    <div class="section-header">
      <h2 class="section-title">
        <i class="pi pi-chart-bar section-icon"></i>
        Analytics & Insights
      </h2>
      <p class="section-subtitle">Analyse détaillée de votre portefeuille et activités de trading</p>
    </div>

    <div class="charts-grid">
      <!-- Répartition par Secteur -->
      <div class="chart-container  sector-chart">
        <div class="chart-header">
          <div class="chart-title-section">
            <h3 class="chart-title">Répartition par Secteur</h3>
            <p class="chart-description">Distribution de vos investissements</p>
          </div>
          <div class="chart-actions">
            <button class="chart-action-btn" title="Exporter">
              <i class="pi pi-download"></i>
            </button>
            <button class="chart-action-btn" title="Plein écran">
              <i class="pi pi-expand"></i>
            </button>
          </div>
        </div>
        <div class="chart-content">
          <div class="chart-wrapper">
            <p-chart type="pie" [data]="pieChartData" [options]="pieChartOptions" class="modern-chart"></p-chart>
          </div>
          <div class="chart-insights">
            <div class="insight-item">
              <span class="insight-label">Secteur dominant</span>
              <span class="insight-value">Technologie</span>
            </div>
            <div class="insight-item">
              <span class="insight-label">Diversification</span>
              <span class="insight-value">Excellente</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Répartition des Ordres par Type -->
      <div class="chart-container orders-type-chart">
        <div class="chart-header">
          <div class="chart-title-section">
            <h3 class="chart-title">Types d'Ordres</h3>
            <p class="chart-description">Répartition de vos ordres de trading</p>
          </div>
          <div class="chart-actions">
            <button class="chart-action-btn" title="Exporter">
              <i class="pi pi-download"></i>
            </button>
            <button class="chart-action-btn" title="Plein écran">
              <i class="pi pi-expand"></i>
            </button>
          </div>
        </div>
        <div class="chart-content">
          <div class="chart-wrapper">
            <p-chart type="doughnut" [data]="typeChartData" [options]="pieChartOptions" class="modern-chart"></p-chart>
          </div>
          <div class="chart-legend-custom">
            <div class="legend-item" *ngFor="let label of typeLabels; let i = index">
              <span class="legend-color" [style.background-color]="dashboardColors[i]"></span>
              <span class="legend-label">{{ label }}</span>
              <span class="legend-value">{{ typeValues[i] }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Statut des Ordres -->
      <div class="chart-container orders-status-chart">
        <div class="chart-header">
          <div class="chart-title-section">
            <h3 class="chart-title">Statut des Ordres</h3>
            <p class="chart-description">Performance et état de vos ordres de trading</p>
          </div>
          <div class="chart-filters">
            <select class="filter-select">
              <option>Dernière semaine</option>
              <option>Dernier mois</option>
              <option>Dernière année</option>
            </select>
            <div class="chart-actions">
              <button class="chart-action-btn" title="Exporter">
                <i class="pi pi-download"></i>
              </button>
              <button class="chart-action-btn" title="Plein écran">
                <i class="pi pi-expand"></i>
              </button>
            </div>
          </div>
        </div>
        <div class="chart-content">
          <div class="chart-wrapper full-height">
            <p-chart type="bar" [data]="statutChartData" [options]="barChartOptions" class="modern-chart"></p-chart>
          </div>
          <div class="chart-stats">
            <div class="stat-card">
              <div class="stat-icon success">
                <i class="pi pi-check-circle"></i>
              </div>
              <div class="stat-info">
                <span class="stat-label">Exécutés</span>
                <span class="stat-value">{{ getExecutedOrdersCount() }}</span>
              </div>
            </div>
            <div class="stat-card">
              <div class="stat-icon pending">
                <i class="pi pi-clock"></i>
              </div>
              <div class="stat-info">
                <span class="stat-label">En attente</span>
                <span class="stat-value">{{ getPendingOrdersCount() }}</span>
              </div>
            </div>
            <div class="stat-card">
              <div class="stat-icon cancelled">
                <i class="pi pi-times-circle"></i>
              </div>
              <div class="stat-info">
                <span class="stat-label">Annulés</span>
                <span class="stat-value">{{ getCancelledOrdersCount() }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Section Actions Rapides -->
  <div class="quick-actions-section">
    <div class="section-header">
      <h2 class="section-title">
        <i class="pi pi-bolt section-icon"></i>
        Actions Rapides
      </h2>
    </div>

    <div class="quick-actions-grid">
      <button class="quick-action-btn primary">
        <div class="action-icon">
          <i class="pi pi-plus"></i>
        </div>
        <div class="action-content">
          <span class="action-title">Nouvel Ordre</span>
          <span class="action-subtitle">Passer un ordre de trading</span>
        </div>
      </button>

      <button class="quick-action-btn secondary">
        <div class="action-icon">
          <i class="pi pi-eye"></i>
        </div>
        <div class="action-content">
          <span class="action-title">Voir Portefeuille</span>
          <span class="action-subtitle">Détails de vos investissements</span>
        </div>
      </button>

      <button class="quick-action-btn tertiary">
        <div class="action-icon">
          <i class="pi pi-chart-line"></i>
        </div>
        <div class="action-content">
          <span class="action-title">Analyse Marché</span>
          <span class="action-subtitle">Tendances et opportunités</span>
        </div>
      </button>

      <button class="quick-action-btn quaternary">
        <div class="action-icon">
          <i class="pi pi-cog"></i>
        </div>
        <div class="action-content">
          <span class="action-title">Paramètres</span>
          <span class="action-subtitle">Configuration du compte</span>
        </div>
      </button>
    </div>
  </div>
</div>