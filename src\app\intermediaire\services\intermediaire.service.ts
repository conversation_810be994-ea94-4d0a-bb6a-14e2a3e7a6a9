import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { InvestisseurDTO } from '../models/Investisseur';
import { OrdreProposeResponseDTO, OrdreProposeSimulationRequest, OrdreProposeSimulationResponseDTO } from '../models/OrdreP';
import { OrdreResponseDTO } from '../../investisseur/models/ordre';
import { OperationSimulationDTO, OperationResponseDTO, OperationExecutionGroupDTO } from '../models/operation-simulation';
import { HistoriqueExecutionDTO, NotificationRequestDTO, DashboardKpiDTO, ActionDTO } from '../models/dashboard';

@Injectable({
  providedIn: 'root'
})
export class IntermediaireService {
  private apiUrl = 'http://localhost:8080/api/intermediaire';


  constructor(private http: HttpClient) { }

  getInvestisseurs(): Observable<InvestisseurDTO[]> {
    return this.http.get<InvestisseurDTO[]>(`${this.apiUrl}/clients`);
  }
  getAllOrdresProposes(): Observable<OrdreProposeResponseDTO[]> {
    return this.http.get<OrdreProposeResponseDTO[]>(`${this.apiUrl}/OrdresProposition`);
  }
  getPropositionsPourInvestisseur(username: string): Observable<OrdreProposeResponseDTO[]> {
    return this.http.get<OrdreProposeResponseDTO[]>(`${this.apiUrl}/par-investisseur/${username}`);
  }
  simulerOrdre(request: OrdreProposeSimulationRequest): Observable<OrdreProposeSimulationResponseDTO> {
    return this.http.post<OrdreProposeSimulationResponseDTO>(`${this.apiUrl}/simulation`, request);
  }

  approuverOrdre(request: OrdreProposeSimulationRequest): Observable<void> {
    return this.http.post<void>(`${this.apiUrl}/approuver`, request);
  }

  refuserOrdre(ordreProposeId: string): Observable<void> {
    const payload = { ordreProposeId };
    return this.http.post<void>(`${this.apiUrl}/refuser`, payload);
  }


  getOrdresValides(): Observable<OrdreResponseDTO[]> {
    return this.http.get<OrdreResponseDTO[]>(`${this.apiUrl}/ordres/valides`);
  }
  getOrdresEnExecution(): Observable<OrdreResponseDTO[]> {
    return this.http.get<OrdreResponseDTO[]>(`${this.apiUrl}/ordres/enExecution`);
  }
  getOrdresExecute(): Observable<OrdreResponseDTO[]> {
    return this.http.get<OrdreResponseDTO[]>(`${this.apiUrl}/ordres/execute`);
  }
  getOrdresSimilaires(ordreId: string): Observable<any[]> {
    return this.http.get<any[]>(`${this.apiUrl}/ordres/${ordreId}/similaires`);
  }

  regrouperOrdres(ordres: OrdreResponseDTO[]): Observable<any> {
    return this.http.post<any>(`${this.apiUrl}/regrouper-ordres`, ordres);
  }

  // Nouvelle méthode pour simuler le fractionnement
  simulerFractionnement(ordreId: string): Observable<OperationSimulationDTO[]> {
    return this.http.get<OperationSimulationDTO[]>(`${this.apiUrl}/ordres/${ordreId}/simuler-fractionnement`);
  }

  // Nouvelle méthode pour valider et envoyer le fractionnement
  validerEtEnvoyerFractionnement(ordreId: string, indexEnvoye: number): Observable<OperationResponseDTO[]> {
    return this.http.post<OperationResponseDTO[]>(`${this.apiUrl}/ordres/${ordreId}/valider-fractionnement/${indexEnvoye}`, {});
  }

  // Méthode pour récupérer les groupes d'exécution d'opérations
  getOperationExecutionGroups(): Observable<OperationExecutionGroupDTO[]> {
    return this.http.get<OperationExecutionGroupDTO[]>(`${this.apiUrl}/operations/execution-groupes`);
  }

  // ===== NOUVELLES MÉTHODES DASHBOARD ===== //

  // Récupérer le top 5 des actions échangées
  getTop5ActionsEchangees(): Observable<Array<{ [key: string]: any }>> {
    return this.http.get<Array<{ [key: string]: any }>>(`${this.apiUrl}/top-actions-echangees`);
  }

  // Récupérer le nombre d'ordres non exécutables
  getOrdresNonExecutables(): Observable<number> {
    return this.http.get<number>(`${this.apiUrl}/ordres-non-executables`);
  }

  // Récupérer la commission des derniers 2 jours
  getCommissionDerniers2Jours(): Observable<number> {
    return this.http.get<number>(`${this.apiUrl}/commission-2jours`);
  }

  // Récupérer le ratio d'exécution sur validés
  getRatioExecutionSurValides(): Observable<number> {
    return this.http.get<number>(`${this.apiUrl}/ratio-execution-valides`);
  }

  // Récupérer la croissance du volume des opérations
  getCroissanceVolumeOperations(): Observable<number> {
    return this.http.get<number>(`${this.apiUrl}/croissance-volume`);
  }

  // Récupérer le nombre d'investisseurs actifs aujourd'hui
  getInvestisseursActifsAujourdhui(): Observable<number> {
    return this.http.get<number>(`${this.apiUrl}/investisseurs-actifs-aujourdhui`);
  }

  // Récupérer tous les KPI du dashboard
  getKpiDashboard(): Observable<{ [key: string]: any }> {
    return this.http.get<{ [key: string]: any }>(`${this.apiUrl}/kpi`);
  }

  // Envoyer une notification
  envoyerNotification(dto: NotificationRequestDTO): Observable<string> {
    return this.http.post<string>(`${this.apiUrl}/envoyer-notification`, dto);
  }

  // Récupérer l'historique d'exécution
  getHistoriqueExecution(): Observable<HistoriqueExecutionDTO[]> {
    return this.http.get<HistoriqueExecutionDTO[]>(`${this.apiUrl}/historique`);
  }

  // Récupérer toutes les actions
  getAllActions(): Observable<ActionDTO[]> {
    return this.http.get<ActionDTO[]>(`${this.apiUrl}/actions`);
  }

}

