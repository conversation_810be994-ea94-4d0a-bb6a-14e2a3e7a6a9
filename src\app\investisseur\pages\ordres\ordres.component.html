<!-- Dialogs et notifications -->
<p-confirmDialog styleClass="modern-confirm-dialog"></p-confirmDialog>
<p-toast position="top-right"></p-toast>

<!-- En-tête ultra-moderne -->
<div class="ordres-header">
  <div class="header-content">
    <div class="header-left">
      <h1 class="ordres-title">
        <i class="pi pi-list-check title-icon"></i>
        Gestion des Ordres
      </h1>
      <p class="ordres-subtitle">Suivez et gérez vos ordres de bourse en temps réel</p>
    </div>
    <div class="header-stats">
      <div class="stat-card">
        <div class="stat-icon">
          <i class="pi pi-clock"></i>
        </div>
        <div class="stat-info">
          <span class="stat-label">En attente</span>
          <span class="stat-value">{{ ordresEnAttente.length || 0 }}</span>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon">
          <i class="pi pi-check-circle"></i>
        </div>
        <div class="stat-info">
          <span class="stat-label">Exécutés</span>
          <span class="stat-value">{{ ordresExecutes.length || 0 }}</span>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon">
          <i class="pi pi-chart-bar"></i>
        </div>
        <div class="stat-info">
          <span class="stat-label">Total</span>
          <span class="stat-value">{{ ordres.length || 0 }}</span>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Navigation par onglets ultra-moderne -->
<div class="modern-tabs-container">
  <div class="modern-tabs">
    <button *ngFor="let item of items; let i = index" class="modern-tab" [class.active]="activeItem === item"
      (click)="onTabChange({value: item})">
      <i [class]="item.icon"></i>
      <span>{{ item.label }}</span>
      <div class="tab-indicator" *ngIf="activeItem === item"></div>
    </button>
  </div>
</div>

<!-- Section Tous les ordres -->
<div *ngIf="activeItem.label === 'Tous les ordres'" class="orders-section">
  <div class="section-header">
    <div class="section-title">
      <i class="pi pi-list"></i>
      <span>Historique complet</span>
    </div>
    <div class="section-actions">
      <button class="filter-btn">
        <i class="pi pi-filter"></i>
        Filtrer
      </button>
    </div>
  </div>

  <div class="modern-table-container">
    <p-table [value]="ordres" [paginator]="true" [rows]="8" styleClass="ultra-modern-table" responsiveLayout="scroll">
      <ng-template pTemplate="header">
        <tr>
          <th class="col-avatar">Action</th>
          <th class="col-isin">ISIN</th>
          <th class="col-type">Type</th>
          <th class="col-status">Statut</th>
          <th class="col-quantity">Quantité</th>
          <th class="col-price-range">Prix</th>
          <th class="col-all-or-nothing">T.O.R</th>
          <th class="col-validity">Validité</th>
        </tr>
      </ng-template>

      <ng-template pTemplate="body" let-ordre let-i="rowIndex">
        <tr class="table-row" [style.animation-delay]="i * 0.05 + 's'">
          <td class="col-avatar">
            <div class="order-info-cell">
              <div class="order-avatar" [ngClass]="getRandomColorClass(ordre.actionNom)">
                {{ ordre.actionNom.charAt(0).toUpperCase() }}
              </div>
              <div class="order-details">
                <div class="order-name">{{ ordre.actionNom }}</div>
                <div class="order-symbol">{{ ordre.actionIsin }}</div>
              </div>
            </div>
          </td>
          <td class="col-isin">
            <span class="isin-badge">{{ ordre.actionIsin }}</span>
          </td>
          <td class="col-type">
            <div class="type-badge" [class]="ordre.type === 'ACHAT' ? 'type-buy' : 'type-sell'">
              <i [class]="ordre.type === 'ACHAT' ? 'pi pi-plus' : 'pi pi-minus'"></i>
              {{ ordre.type }}
            </div>
          </td>
          <td class="col-status">
            <div class="status-badge" [class]="getStatusClass(ordre.statut)">
              <i [class]="getStatusIcon(ordre.statut)"></i>
              {{ ordre.statut }}
            </div>
          </td>
          <td class="col-quantity">
            <div class="quantity-display">
              <span class="quantity-value">{{ ordre.quantite }}</span>
              <span class="quantity-unit">actions</span>
            </div>
          </td>
          <td class="col-price-range">
            <div class="price-range">
              <div class="price-item" *ngIf="ordre.type === 'VENTE' && ordre.prixMin">
                <span class="price-label">Min:</span>
                <span class="price-value">{{ ordre.prixMin | number:'1.2-2' }} TND</span>
              </div>
              <div class="price-item" *ngIf="ordre.type === 'ACHAT' && ordre.prixMax">
                <span class="price-label">Max:</span>
                <span class="price-value">{{ ordre.prixMax | number:'1.2-2' }} TND</span>
              </div>
            </div>
          </td>
          <td class="col-all-or-nothing">
            <div class="boolean-indicator" [class.active]="ordre.toutOuRien">
              <i [class]="ordre.toutOuRien ? 'pi pi-check' : 'pi pi-times'"></i>
            </div>
          </td>
          <td class="col-validity">
            <div class="date-display">
              <i class="pi pi-calendar"></i>
              <span>{{ ordre.dateDeValidite | date: 'dd/MM/yyyy' }}</span>
            </div>
          </td>
        </tr>
      </ng-template>

      <ng-template pTemplate="emptymessage">
        <tr>
          <td colspan="8" class="empty-state">
            <div class="empty-content">
              <div class="empty-icon">
                <i class="pi pi-inbox"></i>
              </div>
              <h3 class="empty-title">Aucun ordre trouvé</h3>
              <p class="empty-text">Vous n'avez pas encore passé d'ordres</p>
            </div>
          </td>
        </tr>
      </ng-template>
    </p-table>
  </div>
</div>


<!-- Section Ordres en attente -->
<div *ngIf="activeItem.label === 'Ordres en attente'" class="orders-section">
  <div class="section-header">
    <div class="section-title">
      <i class="pi pi-clock"></i>
      <span>Ordres en attente</span>
    </div>
    <div class="section-actions">
      <button class="filter-btn">
        <i class="pi pi-filter"></i>
        Filtrer
      </button>
    </div>
  </div>

  <div class="modern-table-container">
    <p-table [value]="ordresEnAttente" [paginator]="true" [rows]="10" styleClass="ultra-modern-table"
      responsiveLayout="scroll">
      <ng-template pTemplate="header">
        <tr>
          <th class="col-avatar">Action</th>
          <th class="col-type">Type</th>
          <th class="col-status">Statut</th>
          <th class="col-quantity">Quantité</th>
          <th class="col-price-range">Prix</th>
          <th class="col-all-or-nothing">T.O.R</th>
          <th class="col-validity">Validité</th>
          <th class="col-actions">Actions</th>
        </tr>
      </ng-template>

      <ng-template pTemplate="body" let-ordre let-i="rowIndex">
        <tr class="table-row" [style.animation-delay]="i * 0.05 + 's'">
          <td class="col-avatar">
            <div class="order-info-cell">
              <div class="order-avatar" [ngClass]="getRandomColorClass(ordre.actionNom)">
                {{ ordre.actionNom.charAt(0).toUpperCase() }}
              </div>
              <div class="order-details">
                <div class="order-name">{{ ordre.actionNom }}</div>
                <div class="order-symbol">{{ ordre.actionIsin }}</div>
              </div>
            </div>
          </td>
          <td class="col-type">
            <div class="type-badge" [class]="ordre.type === 'ACHAT' ? 'type-buy' : 'type-sell'">
              <i [class]="ordre.type === 'ACHAT' ? 'pi pi-plus' : 'pi pi-minus'"></i>
              {{ ordre.type }}
            </div>
          </td>
          <td class="col-status">
            <div class="status-badge status-pending">
              <i class="pi pi-clock"></i>
              EN_ATTENTE
            </div>
          </td>
          <td class="col-quantity">
            <div class="quantity-display">
              <span class="quantity-value">{{ ordre.quantite }}</span>
              <span class="quantity-unit">actions</span>
            </div>
          </td>
          <td class="col-price-range">
            <div class="price-range">
              <div class="price-item" *ngIf="ordre.type === 'VENTE' && ordre.prixMin">
                <span class="price-label">Min:</span>
                <span class="price-value">{{ ordre.prixMin | number:'1.2-2' }} TND</span>
              </div>
              <div class="price-item" *ngIf="ordre.type === 'ACHAT' && ordre.prixMax">
                <span class="price-label">Max:</span>
                <span class="price-value">{{ ordre.prixMax | number:'1.2-2' }} TND</span>
              </div>
            </div>
          </td>
          <td class="col-all-or-nothing">
            <div class="boolean-indicator" [class.active]="ordre.toutOuRien">
              <i [class]="ordre.toutOuRien ? 'pi pi-check' : 'pi pi-times'"></i>
            </div>
          </td>
          <td class="col-validity">
            <div class="date-display">
              <i class="pi pi-calendar"></i>
              <span>{{ ordre.dateDeValidite | date: 'dd/MM/yyyy' }}</span>
            </div>
          </td>
          <td class="col-actions">
            <div class="action-buttons">
              <button pButton type="button" icon="pi pi-pencil" class="p-button edit"
                (click)="modifierOrdreEnAttente(ordre)" pTooltip="Modifier">
              </button>
              <button pButton type="button" icon="pi pi-times" class="p-button cancel"
                (click)="annulerOrdreEnAttente(ordre)" pTooltip="Annuler">
              </button>
            </div>
          </td>
        </tr>
      </ng-template>

      <ng-template pTemplate="emptymessage">
        <tr>
          <td colspan="9" class="empty-state">
            <div class="empty-content">
              <div class="empty-icon">
                <i class="pi pi-clock"></i>
              </div>
              <h3 class="empty-title">Aucun ordre en attente</h3>
              <p class="empty-text">Tous vos ordres ont été traités</p>
            </div>
          </td>
        </tr>
      </ng-template>
    </p-table>
  </div>
  <p-dialog header="Modifier l'ordre" [(visible)]="popupModificationVisible" [modal]="true" [style]="{ width: '40rem' }"
    [breakpoints]="{ '960px': '90vw' }">
    <form [formGroup]="modificationForm" (ngSubmit)="confirmerModification()" class="modification-form">

      <div class="form-grid">
        <div class="form-group">
          <label>Quantité</label>
          <input pInputText type="number" formControlName="quantite" />
        </div>

        <!-- Prix min pour les ordres de VENTE -->
        <div class="form-group" *ngIf="selectedOrdre?.type === 'VENTE'">
          <label>Prix minimum (TND)</label>
          <input pInputText type="number" formControlName="prixMin" step="0.01" placeholder="Ex: 25.50" />
        </div>

        <!-- Prix max pour les ordres d'ACHAT -->
        <div class="form-group" *ngIf="selectedOrdre?.type === 'ACHAT'">
          <label>Prix maximum (TND)</label>
          <input pInputText type="number" formControlName="prixMax" step="0.01" placeholder="Ex: 30.00" />
        </div>

        <div class="form-group">
          <label>Date de validité</label>
          <input pInputText type="date" formControlName="dateDeValidite" />
          <small class="p-error" *ngIf="modificationForm.get('dateDeValidite')?.hasError('datePassee')">
            La date ne peut pas être dans le passé.
          </small>
        </div>

        <div class="form-group-full">
          <p-checkbox formControlName="toutOuRien" binary="true" inputId="toutOuRien"></p-checkbox>
          <label for="toutOuRien">Tout ou rien</label>
        </div>
      </div>

      <div class="dialog-footer">
        <button pButton type="button" label="Annuler" class="p-button-secondary"
          (click)="popupModificationVisible = false"></button>
        <button pButton type="submit" label="Valider" class="p-button-success" [disabled]="modificationForm.invalid"
          (click)="confirmerModification()"></button>
      </div>
    </form>
  </p-dialog>

</div>
<!-- Section Ordres exécutés -->
<div *ngIf="activeItem.label === 'Ordres executés'" class="orders-section">
  <div class="section-header">
    <div class="section-title">
      <i class="pi pi-check-circle"></i>
      <span>Ordres exécutés</span>
    </div>
    <div class="section-actions">
      <button class="filter-btn">
        <i class="pi pi-filter"></i>
        Filtrer
      </button>
    </div>
  </div>

  <div class="modern-table-container">
    <p-table [value]="ordresExecutes" [paginator]="true" [rows]="10" styleClass="ultra-modern-table"
      responsiveLayout="scroll">
      <ng-template pTemplate="header">
        <tr>
          <th class="col-avatar">Action</th>
          <th class="col-isin">ISIN</th>
          <th class="col-type">Type</th>
          <th class="col-status">Statut</th>
          <th class="col-quantity">Quantité</th>
          <th class="col-price-range">Prix</th>
          <th class="col-all-or-nothing">T.O.R</th>
          <th class="col-validity">Validité</th>
        </tr>
      </ng-template>

      <ng-template pTemplate="body" let-ordre let-i="rowIndex">
        <tr class="table-row" [style.animation-delay]="i * 0.05 + 's'">
          <td class="col-avatar">
            <div class="order-info-cell">
              <div class="order-avatar" [ngClass]="getRandomColorClass(ordre.actionNom)">
                {{ ordre.actionNom.charAt(0).toUpperCase() }}
              </div>
              <div class="order-details">
                <div class="order-name">{{ ordre.actionNom }}</div>
                <div class="order-symbol">{{ ordre.actionIsin }}</div>
              </div>
            </div>
          </td>
          <td class="col-isin">
            <span class="isin-badge">{{ ordre.actionIsin }}</span>
          </td>
          <td class="col-type">
            <div class="type-badge" [class]="ordre.type === 'ACHAT' ? 'type-buy' : 'type-sell'">
              <i [class]="ordre.type === 'ACHAT' ? 'pi pi-plus' : 'pi pi-minus'"></i>
              {{ ordre.type }}
            </div>
          </td>
          <td class="col-status">
            <div class="status-badge" [class]="getStatusClass(ordre.statut)">
              <i [class]="getStatusIcon(ordre.statut)"></i>
              {{ ordre.statut }}
            </div>
          </td>
          <td class="col-quantity">
            <div class="quantity-display">
              <span class="quantity-value">{{ ordre.quantite }}</span>
              <span class="quantity-unit">actions</span>
            </div>
          </td>
          <td class="col-price-range">
            <div class="price-range">
              <div class="price-item" *ngIf="ordre.type === 'VENTE' && ordre.prixMin">
                <span class="price-label">Min:</span>
                <span class="price-value">{{ ordre.prixMin | number:'1.2-2' }} TND</span>
              </div>
              <div class="price-item" *ngIf="ordre.type === 'ACHAT' && ordre.prixMax">
                <span class="price-label">Max:</span>
                <span class="price-value">{{ ordre.prixMax | number:'1.2-2' }} TND</span>
              </div>
            </div>
          </td>
          <td class="col-all-or-nothing">
            <div class="boolean-indicator" [class.active]="ordre.toutOuRien">
              <i [class]="ordre.toutOuRien ? 'pi pi-check' : 'pi pi-times'"></i>
            </div>
          </td>
          <td class="col-validity">
            <div class="date-display">
              <i class="pi pi-calendar"></i>
              <span>{{ ordre.dateDeValidite | date: 'dd/MM/yyyy' }}</span>
            </div>
          </td>
        </tr>
      </ng-template>

      <ng-template pTemplate="emptymessage">
        <tr>
          <td colspan="8" class="empty-state">
            <div class="empty-content">
              <div class="empty-icon">
                <i class="pi pi-check-circle"></i>
              </div>
              <h3 class="empty-title">Aucun ordre exécuté</h3>
              <p class="empty-text">Vos ordres exécutés apparaîtront ici</p>
            </div>
          </td>
        </tr>
      </ng-template>
    </p-table>
  </div>
</div>