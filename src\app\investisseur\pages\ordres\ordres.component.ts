import { Component, OnInit } from '@angular/core';
import { RouterModule } from '@angular/router';
import { CommonModule } from '@angular/common';
import { TabMenuModule } from 'primeng/tabmenu';
import { ConfirmationService, MenuItem, MessageService } from 'primeng/api';
import { TableModule } from 'primeng/table';
import { AbstractControl, FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, ValidationErrors, Validators } from '@angular/forms';
import { DropdownModule } from 'primeng/dropdown';
import { OrdreResponseDTO, UpdateOrdreRequest } from '../../models/ordre';
import { OrdreService } from '../../services/ordre.service';
import { AuthService } from '../../../auth/auth.service';
import { MultiSelectModule } from 'primeng/multiselect';
import { TagModule } from 'primeng/tag';
import { Color } from 'chart.js';
import { TooltipModule } from 'primeng/tooltip';
import { ButtonModule } from 'primeng/button';
import { MatIcon } from '@angular/material/icon';
import { DialogModule } from 'primeng/dialog';
import { InputTextModule } from 'primeng/inputtext';
import { CheckboxModule } from 'primeng/checkbox';
import { ToastModule } from 'primeng/toast';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { OrdreProposeResponseDTO } from '../../../intermediaire/models/OrdreP';


@Component({
  selector: 'app-ordres',
  standalone: true,
  imports: [

    RouterModule,
    TabMenuModule,
    TableModule,
    DropdownModule,
    MultiSelectModule,
    FormsModule,
    CommonModule,
    TagModule,
    TooltipModule,
    ButtonModule, CommonModule,
    MatIcon,
    DialogModule,
    ReactiveFormsModule,
    InputTextModule,
    CheckboxModule,
    ToastModule,
    ConfirmDialogModule,

  ],
  templateUrl: './ordres.component.html',
  styleUrl: './ordres.component.css'
})
export class OrdresComponent implements OnInit {
  items: MenuItem[] = [];
  activeItem!: MenuItem;
  visible = false;
  username = '';
  modificationForm!: FormGroup;
  popupModificationVisible = false;
  selectedOrdre!: OrdreResponseDTO;
  ordresEnAttente: OrdreProposeResponseDTO[] = [];
  ordresExecutes: OrdreResponseDTO[] = [];

  ordres: OrdreResponseDTO[] = [];

  constructor(private ordreService: OrdreService, private authService: AuthService, private messageService: MessageService, private fb: FormBuilder, private confirmationService: ConfirmationService,


  ) { }

  ngOnInit(): void {

    this.items = [
      { label: 'Tous les ordres', icon: 'pi pi-list' },
      { label: 'Ordres en attente', icon: '	pi pi-history' },
      { label: 'Ordres executés', icon: '	pi pi-check-circle' }
    ];
    this.activeItem = this.items[0]; // Par défaut

    this.username = this.authService.getUsername();
    console.log('Username connecté:', this.username);
    console.log('Token:', localStorage.getItem('token'));
    console.log('Role:', localStorage.getItem('role'));

    // Test de connectivité au backend
    this.testBackendConnectivity();

    this.ordreService.getMyOrdres().subscribe({
      next: (data) => {
        console.log('Tous les ordres reçus:', data);
        this.ordres = data;
      },
      error: (err) => console.error('Erreur lors du chargement des ordres', err)
    });
    // Ordres en attente
    this.ordreService.getOrdresEnAttentePourMoi().subscribe({
      next: (data) => {
        console.log('Ordres en attente reçus:', data);
        this.ordresEnAttente = data;
      },
      error: (err) => {
        console.error('Erreur lors du chargement des ordres en attente:', err);
      }
    });

    // Ordres exécutés
    this.ordreService.getMesOrdresAvecStatut('EXECUTE').subscribe({
      next: (data) => {
        console.log('Ordres exécutés reçus:', data);
        this.ordresExecutes = data;
      },
      error: (err) => {
        console.error('Erreur lors du chargement des ordres exécutés:', err);
      }
    });
    this.modificationForm = this.fb.group({
      quantite: [null, Validators.required],
      prixMin: [null],
      prixMax: [null],
      dateDeValidite: [null, [Validators.required, this.dateNonPasseeValidator]],
      toutOuRien: [false]
    });

  }
  dateNonPasseeValidator(control: AbstractControl): ValidationErrors | null {
    const today = new Date();
    const selected = new Date(control.value);
    if (selected < today) {
      return { datePassee: true };
    }
    return null;
  }

  configurerValidateursSelenType(type: string): void {
    const prixMinControl = this.modificationForm.get('prixMin');
    const prixMaxControl = this.modificationForm.get('prixMax');

    if (type === 'VENTE') {
      // Pour les ordres de vente, seul prixMin est requis
      prixMinControl?.setValidators([Validators.required, Validators.min(0)]);
      prixMaxControl?.clearValidators();
      prixMaxControl?.setValue(null);
    } else if (type === 'ACHAT') {
      // Pour les ordres d'achat, seul prixMax est requis
      prixMaxControl?.setValidators([Validators.required, Validators.min(0)]);
      prixMinControl?.clearValidators();
      prixMinControl?.setValue(null);
    }

    // Mettre à jour la validité des contrôles
    prixMinControl?.updateValueAndValidity();
    prixMaxControl?.updateValueAndValidity();
  }

  onTabChange(event: any) {
    this.activeItem = event.value;
  }

  getSecteurIcon(secteur: string): string {
    switch (secteur.toLowerCase()) {
      case 'technologie': return 'pi pi-desktop';
      case 'finance': return 'pi pi-wallet';
      case 'industrie': return 'pi pi-cog';
      case 'e-commerce': return 'pi pi-shopping-cart';
      default: return 'pi pi-briefcase';
    }
  }
  getRandomColorClass(actionNom: string): string {
    const colorClasses = [
      "C1", "C2", "C3", "C4", "C5", "C6", "C7", "C8", "C9", "C10"
    ];
    const index = actionNom.charCodeAt(0) % colorClasses.length;
    return colorClasses[index];
  }


  getStatutColor(statut: string): 'info' | 'success' | 'danger' | 'warning' {
    switch (statut) {
      case 'EN_ATTENTE': return 'warning';
      case 'EXECUTE': return 'success';
      case 'ANNULE': return 'danger';
      default: return 'info';
    }
  }
  modifierOrdre(ordre: OrdreResponseDTO): void {
    this.selectedOrdre = ordre;

    // Configurer les validateurs selon le type d'ordre
    this.configurerValidateursSelenType(ordre.type);

    this.modificationForm.patchValue({
      quantite: ordre.quantite,
      prixMin: ordre.prixMin,
      prixMax: ordre.prixMax,
      dateDeValidite: ordre.dateDeValidite,
      toutOuRien: ordre.toutOuRien
    });
    this.popupModificationVisible = true;
  }

  validerModification(): void {
    const formValue = this.modificationForm.value;

    // Préparer la requête selon le type d'ordre
    const updated: UpdateOrdreRequest = {
      quantite: formValue.quantite,
      prixMin: this.selectedOrdre.type === 'VENTE' ? formValue.prixMin : 0,
      prixMax: this.selectedOrdre.type === 'ACHAT' ? formValue.prixMax : 0,
      toutOuRien: formValue.toutOuRien,
      dateDeValidite: formValue.dateDeValidite
    };

    this.ordreService.modifierOrdre(this.selectedOrdre.id, updated).subscribe({
      next: () => {
        this.messageService.add({ severity: 'success', summary: 'Succès', detail: 'Ordre modifié avec succès' });
        this.popupModificationVisible = false;
        this.rechargerOrdres(); // pour rafraîchir les données
      },
      error: err => {
        this.messageService.add({ severity: 'error', summary: 'Erreur', detail: err.error?.message || 'Erreur lors de la modification' });
      }
    });
  }
  confirmerModification(): void {
    this.confirmationService.confirm({
      message: 'Voulez-vous vraiment modifier cet ordre ?',
      header: 'Confirmation de modification',
      icon: 'pi pi-exclamation-triangle',
      acceptLabel: 'Oui',
      rejectLabel: 'Non',
      accept: () => this.validerModification(),
      reject: () => {
        this.messageService.add({
          severity: 'info',
          summary: 'Annulé',
          detail: 'Modification annulée'
        });
      }
    });
  }

  rechargerOrdres(): void {
    this.ordreService.getMyOrdres().subscribe({
      next: (data) => this.ordres = data,
      error: (err) => console.error('Erreur lors du rechargement des ordres', err)
    });

    this.ordreService.getOrdresEnAttentePourMoi().subscribe(data => {
      this.ordresEnAttente = data;
    });

    this.ordreService.getMesOrdresAvecStatut('EXECUTE').subscribe(data => {
      this.ordresExecutes = data;
    });
  }


  annulerOrdre(ordre: OrdreResponseDTO): void {
    this.confirmationService.confirm({
      message: 'Voulez-vous vraiment annuler cet ordre ?',
      header: 'Confirmation d’annulation',
      icon: 'pi pi-exclamation-triangle',
      acceptLabel: 'Oui',
      rejectLabel: 'Non',
      accept: () => {
        this.ordreService.annulerOrdre(ordre.id).subscribe({
          next: () => {
            this.messageService.add({
              severity: 'success',
              summary: 'Ordre annulé',
              detail: `L’ordre a bien été annulé.`,
            });
            this.rechargerOrdres(); // Pour mettre à jour l'affichage
          },
          error: () => {
            this.messageService.add({
              severity: 'error',
              summary: 'Erreur',
              detail: `Échec de l’annulation de l’ordre.`,
            });
          }
        });
      }
    });
  }

  // Méthodes spécifiques pour les ordres en attente (OrdreProposeResponseDTO)
  modifierOrdreEnAttente(ordre: OrdreProposeResponseDTO): void {
    // Convertir OrdreProposeResponseDTO vers OrdreResponseDTO pour la compatibilité
    const ordreConverti: OrdreResponseDTO = {
      id: ordre.id,
      type: ordre.type,
      statut: 'EN_ATTENTE', // Les ordres en attente ont toujours ce statut
      quantite: ordre.quantite,
      prixMin: ordre.prixMin,
      prixMax: ordre.prixMax,
      toutOuRien: ordre.toutOuRien,
      dateDeReception: '', // Pas disponible dans OrdreProposeResponseDTO
      dateDeValidite: ordre.dateDeValidite,
      investisseurUsername: ordre.investisseurUsername,
      actionIsin: ordre.actionIsin,
      actionNom: ordre.actionNom
    };
    this.modifierOrdre(ordreConverti);
  }

  annulerOrdreEnAttente(ordre: OrdreProposeResponseDTO): void {
    // Convertir OrdreProposeResponseDTO vers OrdreResponseDTO pour la compatibilité
    const ordreConverti: OrdreResponseDTO = {
      id: ordre.id,
      type: ordre.type,
      statut: 'EN_ATTENTE',
      quantite: ordre.quantite,
      prixMin: ordre.prixMin,
      prixMax: ordre.prixMax,
      toutOuRien: ordre.toutOuRien,
      dateDeReception: '',
      dateDeValidite: ordre.dateDeValidite,
      investisseurUsername: ordre.investisseurUsername,
      actionIsin: ordre.actionIsin,
      actionNom: ordre.actionNom
    };
    this.annulerOrdre(ordreConverti);
  }

  testBackendConnectivity(): void {
    console.log('🔍 Test de connectivité au backend...');

    // Test simple avec fetch pour vérifier si le backend répond
    fetch('http://localhost:8080/api/test', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
        'Content-Type': 'application/json'
      }
    })
      .then(response => {
        console.log('✅ Backend répond:', response.status);
        if (!response.ok) {
          console.warn('⚠️ Réponse non-OK du backend:', response.status, response.statusText);
        }
        return response.text();
      })
      .then(data => {
        console.log('📦 Données reçues du backend:', data);
      })
      .catch(error => {
        console.error('❌ Erreur de connectivité au backend:', error);
        console.error('🔧 Vérifiez que le backend Spring Boot est démarré sur http://localhost:8080');
      });
  }

  getStatusClass(statut: string): string {
    switch (statut) {
      case 'EN_ATTENTE': return 'status-pending';
      case 'EXECUTE': return 'status-executed';
      case 'ANNULE': return 'status-cancelled';
      case 'EXPIRE': return 'status-expired';
      default: return 'status-default';
    }
  }

  getStatusIcon(statut: string): string {
    switch (statut) {
      case 'EN_ATTENTE': return 'pi pi-clock';
      case 'EXECUTE': return 'pi pi-check-circle';
      case 'ANNULE': return 'pi pi-times-circle';
      case 'EXPIRE': return 'pi pi-exclamation-triangle';
      default: return 'pi pi-info-circle';
    }
  }

}
