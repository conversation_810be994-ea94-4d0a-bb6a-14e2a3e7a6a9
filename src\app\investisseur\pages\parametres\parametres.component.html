<!-- Toast pour les notifications -->
<p-toast></p-toast>

<!-- Container principal ultra-moderne -->
<div class="parametres-container">
  <!-- En-tête spectaculaire -->
  <div class="parametres-header">
    <div class="header-content">
      <div class="header-left">
        <h1 class="parametres-title">
          <i class="pi pi-cog title-icon"></i>
          Paramètres du Compte
        </h1>
        <p class="parametres-subtitle">Gérez votre profil et la sécurité de votre compte</p>
      </div>
      <div class="header-avatar">
        <div class="user-avatar">
          <i class="pi pi-user"></i>
        </div>
        <div class="user-info" *ngIf="userProfile">
          <span class="user-name">{{ userProfile.prenom }} {{ userProfile.nom }}</span>
          <span class="user-email">{{ userProfile.email }}</span>
        </div>
      </div>
    </div>
  </div>

  <!-- Navigation par onglets ultra-moderne -->
  <div class="modern-tabs-container">
    <div class="modern-tabs">
      <button *ngFor="let item of items; let i = index" class="modern-tab" [class.active]="activeItem === item"
        (click)="onTabChange({value: item})">
        <i [class]="item.icon"></i>
        <span>{{ item.label }}</span>
        <div class="tab-indicator" *ngIf="activeItem === item"></div>
      </button>
    </div>
  </div>

  <!-- Section Modifier le profil -->
  <div *ngIf="activeItem.label === 'Modifier le profil'" class="section-container">
    <div class="section-header">
      <div class="section-title">
        <i class="pi pi-user-edit"></i>
        <span>Informations Personnelles</span>
      </div>
      <div class="section-description">
        Mettez à jour vos informations personnelles et de contact
      </div>
    </div>

    <div class="modern-card">
      <form [formGroup]="profileForm" (ngSubmit)="onSubmit()">
        <div class="form-grid">
          <!-- Informations de base -->
          <div class="form-section">
            <h3 class="section-subtitle">
              <i class="pi pi-id-card"></i>
              Identité
            </h3>
            <div class="form-row">
              <div class="form-group">
                <label for="username">Nom d'utilisateur</label>
                <div class="input-wrapper">
                  <i class="pi pi-user input-icon"></i>
                  <input id="username" type="text" pInputText formControlName="username" class="modern-input"
                    readonly />
                </div>
              </div>
              <div class="form-group">
                <label for="cin">CIN</label>
                <div class="input-wrapper">
                  <i class="pi pi-id-card input-icon"></i>
                  <input id="cin" type="text" pInputText formControlName="cin" class="modern-input" />
                </div>
              </div>
            </div>
            <div class="form-row">
              <div class="form-group">
                <label for="nom">Nom</label>
                <div class="input-wrapper">
                  <i class="pi pi-user input-icon"></i>
                  <input id="nom" type="text" pInputText formControlName="nom" class="modern-input" />
                </div>
              </div>
              <div class="form-group">
                <label for="prenom">Prénom</label>
                <div class="input-wrapper">
                  <i class="pi pi-user input-icon"></i>
                  <input id="prenom" type="text" pInputText formControlName="prenom" class="modern-input" />
                </div>
              </div>
            </div>
          </div>

          <!-- Informations de contact -->
          <div class="form-section">
            <h3 class="section-subtitle">
              <i class="pi pi-envelope"></i>
              Contact
            </h3>
            <div class="form-row">
              <div class="form-group full-width">
                <label for="email">Adresse Email</label>
                <div class="input-wrapper">
                  <i class="pi pi-envelope input-icon"></i>
                  <input id="email" type="email" pInputText formControlName="email" class="modern-input" />
                </div>
              </div>
            </div>
            <div class="form-row">
              <div class="form-group full-width">
                <label for="telephone">Téléphone</label>
                <div class="input-wrapper">
                  <i class="pi pi-phone input-icon"></i>
                  <input id="telephone" type="text" pInputText formControlName="telephone" class="modern-input" />
                </div>
              </div>
            </div>
          </div>

          <!-- Adresse -->
          <div class="form-section">
            <h3 class="section-subtitle">
              <i class="pi pi-map-marker"></i>
              Adresse
            </h3>
            <div class="form-row">
              <div class="form-group full-width">
                <label for="adresse">Adresse</label>
                <div class="input-wrapper">
                  <i class="pi pi-home input-icon"></i>
                  <input id="adresse" type="text" pInputText formControlName="adresse" class="modern-input" />
                </div>
              </div>
            </div>
            <div class="form-row">
              <div class="form-group">
                <label for="ville">Ville</label>
                <div class="input-wrapper">
                  <i class="pi pi-building input-icon"></i>
                  <input id="ville" type="text" pInputText formControlName="ville" class="modern-input" />
                </div>
              </div>
              <div class="form-group">
                <label for="codePostal">Code Postal</label>
                <div class="input-wrapper">
                  <i class="pi pi-map input-icon"></i>
                  <input id="codePostal" type="text" pInputText formControlName="codePostal" class="modern-input" />
                </div>
              </div>
            </div>
            <div class="form-row">
              <div class="form-group full-width">
                <label for="pays">Pays</label>
                <div class="input-wrapper">
                  <i class="pi pi-globe input-icon"></i>
                  <input id="pays" type="text" pInputText formControlName="pays" class="modern-input" />
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Actions du formulaire -->
        <div class="form-actions">
          <button type="button" class="btn-secondary">
            <i class="pi pi-times"></i>
            Annuler
          </button>
          <button type="submit" class="btn-primary" [disabled]="loading">
            <i class="pi pi-save"></i>
            <span *ngIf="!loading">Enregistrer les modifications</span>
            <span *ngIf="loading">Enregistrement...</span>
          </button>
        </div>
      </form>
    </div>
  </div>

  <!-- Section Sécurité du compte -->
  <div *ngIf="activeItem.label === 'Sécurité du compte'" class="section-container">
    <div class="section-header">
      <div class="section-title">
        <i class="pi pi-shield"></i>
        <span>Sécurité du Compte</span>
      </div>
      <div class="section-description">
        Modifiez votre mot de passe pour sécuriser votre compte
      </div>
    </div>

    <div class="modern-card">
      <!-- Informations de sécurité -->
      <div class="security-info">
        <div class="security-item">
          <div class="security-icon">
            <i class="pi pi-lock"></i>
          </div>
          <div class="security-content">
            <h4>Mot de passe fort</h4>
            <p>Utilisez au moins 8 caractères avec des lettres, chiffres et symboles</p>
          </div>
        </div>
        <div class="security-item">
          <div class="security-icon">
            <i class="pi pi-eye-slash"></i>
          </div>
          <div class="security-content">
            <h4>Confidentialité</h4>
            <p>Ne partagez jamais votre mot de passe avec d'autres personnes</p>
          </div>
        </div>
        <div class="security-item">
          <div class="security-icon">
            <i class="pi pi-refresh"></i>
          </div>
          <div class="security-content">
            <h4>Mise à jour régulière</h4>
            <p>Changez votre mot de passe régulièrement pour plus de sécurité</p>
          </div>
        </div>
      </div>

      <form [formGroup]="passwordForm" (ngSubmit)="onResetPassword()">
        <div class="form-grid">
          <div class="form-section">
            <h3 class="section-subtitle">
              <i class="pi pi-key"></i>
              Modification du Mot de Passe
            </h3>

            <div class="form-row">
              <div class="form-group full-width">
                <label for="currentPassword">Mot de passe actuel</label>
                <div class="input-wrapper">
                  <i class="pi pi-lock input-icon"></i>
                  <p-password id="currentPassword" formControlName="currentPassword" [toggleMask]="true"
                    [feedback]="false" class="modern-password" placeholder="Entrez votre mot de passe actuel" />
                </div>
                <div class="error-message"
                  *ngIf="passwordForm.controls['currentPassword'].invalid && passwordForm.controls['currentPassword'].touched">
                  <i class="pi pi-exclamation-triangle"></i>
                  Ce champ est requis
                </div>
              </div>
            </div>

            <div class="form-row">
              <div class="form-group full-width">
                <label for="newPassword">Nouveau mot de passe</label>
                <div class="input-wrapper">
                  <i class="pi pi-key input-icon"></i>
                  <p-password id="newPassword" formControlName="newPassword" [toggleMask]="true" [feedback]="true"
                    class="modern-password" placeholder="Entrez votre nouveau mot de passe" />
                </div>
                <div class="error-message"
                  *ngIf="passwordForm.controls['newPassword'].invalid && passwordForm.controls['newPassword'].touched">
                  <i class="pi pi-exclamation-triangle"></i>
                  Le mot de passe doit contenir au moins 8 caractères
                </div>
              </div>
            </div>

            <!-- Indicateur de force du mot de passe -->
            <div class="password-strength" *ngIf="passwordForm.controls['newPassword'].value">
              <div class="strength-label">Force du mot de passe :</div>
              <div class="strength-bar">
                <div class="strength-fill" [class]="getPasswordStrength()"></div>
              </div>
              <div class="strength-text" [class]="getPasswordStrength()">
                {{ getPasswordStrengthText() }}
              </div>
            </div>
          </div>
        </div>

        <!-- Actions du formulaire -->
        <div class="form-actions">
          <button type="button" class="btn-secondary">
            <i class="pi pi-times"></i>
            Annuler
          </button>
          <button type="submit" class="btn-warning" [disabled]="passwordForm.invalid || loading">
            <i class="pi pi-refresh"></i>
            <span *ngIf="!loading">Modifier le mot de passe</span>
            <span *ngIf="loading">Modification...</span>
          </button>
        </div>
      </form>
    </div>
  </div>
</div>